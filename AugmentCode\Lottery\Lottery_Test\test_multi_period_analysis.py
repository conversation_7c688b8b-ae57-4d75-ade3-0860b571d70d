"""
测试多期分析比对功能
"""

from main import LotteryPredictionSystem


def test_multi_period_analysis():
    """测试多期分析比对功能"""
    print("=== 测试多期分析比对功能 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 选择测试期号
    target_period = "25060"
    target_period_int = int(target_period)
    periods = 500
    
    print(f"测试期号: {target_period}")
    print(f"使用数据库范围: 最新{periods}期")
    
    # 进行5期分析测试
    test_periods = 5
    all_results = []
    
    for i in range(test_periods):
        current_target = target_period_int + i
        print(f"\n正在分析期号: {current_target} ({i+1}/{test_periods})")
        
        # 更新当前数据库
        current_target_idx = system.raw_data[system.raw_data['NO'] == current_target].index[0]
        start_idx = max(0, current_target_idx + 1 - periods)
        system.current_db = system.raw_data.iloc[start_idx:current_target_idx + 1].copy()
        
        # 获取答案数据
        answer_data = system.data_loader.get_answer_data(current_target, 6)
        if answer_data is None:
            print(f"期号 {current_target} 后续数据不足，跳过")
            break
        
        # 进行预测
        try:
            # 获取最新期信息
            latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
            
            # 计算必要的概率
            historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
            cold_probs = system.core_algo.calculate_cold_ball_probability(system.current_db)
            repeat_prob = system.core_algo.calculate_repeat_probability(system.current_db)
            follow_matrices = system.core_algo.calculate_following_probability(system.current_db)
            markov_probs = system.core_algo.calculate_markov_chain(
                latest_numbers, follow_matrices, historical_probs
            )
            bayesian_probs = system.core_algo.calculate_bayesian_probability(
                latest_numbers, follow_matrices, historical_probs
            )
            
            # 预测所有10组
            predictions = []
            
            # 第1组：马尔科夫链
            red1, blue1 = system.prediction_algo.predict_group_1_markov(markov_probs)
            predictions.append(red1 + [blue1])
            
            # 第2组：贝叶斯概率
            red2, blue2 = system.prediction_algo.predict_group_2_bayesian(bayesian_probs)
            predictions.append(red2 + [blue2])
            
            # 第3组：历史出现概率
            red3, blue3 = system.prediction_algo.predict_group_3_historical(historical_probs)
            predictions.append(red3 + [blue3])
            
            # 第4组：马尔科夫链+大球筛选
            red4, blue4 = system.prediction_algo.predict_group_4_markov_big(
                markov_probs, historical_probs, historical_probs
            )
            predictions.append(red4 + [blue4])
            
            # 第5组：马尔科夫链+冷球筛选
            red5, blue5 = system.prediction_algo.predict_group_5_markov_cold(
                markov_probs, historical_probs, cold_probs, system.current_db
            )
            predictions.append(red5 + [blue5])
            
            # 第6组：马尔科夫链+重号筛选
            red6, blue6 = system.prediction_algo.predict_group_6_markov_repeat(
                markov_probs, historical_probs, repeat_prob, latest_numbers
            )
            predictions.append(red6 + [blue6])
            
            # 第7组：马尔科夫链+大球冷球筛选
            red7, blue7 = system.advanced_pred.predict_group_7_markov_big_cold(
                markov_probs, historical_probs, historical_probs, cold_probs, system.current_db
            )
            predictions.append(red7 + [blue7])
            
            # 第8组：马尔科夫链+大球重号筛选
            red8, blue8 = system.advanced_pred.predict_group_8_markov_big_repeat(
                markov_probs, historical_probs, historical_probs, repeat_prob, latest_numbers
            )
            predictions.append(red8 + [blue8])
            
            # 第9组：马尔科夫链+冷球重号筛选
            red9, blue9 = system.advanced_pred.predict_group_9_markov_cold_repeat(
                markov_probs, historical_probs, cold_probs, repeat_prob, system.current_db, latest_numbers
            )
            predictions.append(red9 + [blue9])
            
            # 第10组：马尔科夫链+大球冷球重号筛选
            red10, blue10 = system.advanced_pred.predict_group_10_markov_big_cold_repeat(
                markov_probs, historical_probs, historical_probs, cold_probs, repeat_prob, 
                system.current_db, latest_numbers
            )
            predictions.append(red10 + [blue10])
            
            # 准备答案数据
            answers = []
            for _, row in answer_data.iterrows():
                answer = [row['r1'], row['r2'], row['r3'], row['r4'], row['r5'], row['r6'], row['b']]
                answers.append(answer)
            
            # 比对
            comparison_result = system.comparison_algo.compare_predictions_with_answers(predictions, answers)
            
            # 格式化结果
            formatted_result = system.comparison_algo.format_comparison_result(
                str(current_target), predictions, answers, comparison_result
            )
            
            all_results.append(formatted_result)
            
            # 打印当前期的比对结果
            print(f"期号 {current_target} 比对结果:")
            for group_name, group_result in comparison_result.items():
                pred = group_result['prediction']
                red_str = ' '.join(map(str, sorted(pred[:6])))
                print(f"  {group_name}: {red_str} + {pred[6]} -> 最大命中 {group_result['max_hits']} 球")
            
        except Exception as e:
            print(f"分析期号 {current_target} 时出错: {e}")
            continue
    
    # 保存结果
    if all_results:
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"多期分析测试结果_{target_period}_{timestamp}.xlsx"
        system.comparison_algo.save_comparison_results(all_results, output_file)
        
        # 打印最终统计
        distribution = system.comparison_algo.analyze_hit_distribution(all_results)
        print(f"\n=== 最终命中分布统计 ===")
        for group_name, group_dist in distribution.items():
            total = sum(group_dist.values())
            if total > 0:
                avg_hits = sum(hits * count for hits, count in group_dist.items()) / total
                max_hits = max([hits for hits, count in group_dist.items() if count > 0])
                print(f"{group_name}: 总计 {total} 期, 平均命中 {avg_hits:.2f} 球, 最高命中 {max_hits} 球")
        
        print(f"\n✅ 多期分析测试完成！分析了 {len(all_results)} 期数据。")
        print(f"结果已保存到: {output_file}")
        
        # 验证所有组都有数据
        all_groups_have_data = True
        for group_name, group_dist in distribution.items():
            total = sum(group_dist.values())
            if total != len(all_results):
                print(f"❌ {group_name} 数据不完整: {total}/{len(all_results)}")
                all_groups_have_data = False
        
        if all_groups_have_data:
            print("✅ 所有10组预测都有完整的数据！")
        
    else:
        print("❌ 没有生成任何分析结果")


def main():
    """主测试函数"""
    print("多期分析比对功能测试")
    print("=" * 50)
    
    test_multi_period_analysis()
    
    print("\n" + "=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()
