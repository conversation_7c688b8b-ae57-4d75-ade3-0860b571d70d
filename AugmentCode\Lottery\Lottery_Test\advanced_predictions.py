"""
高级预测算法模块
包含第7-10组复合预测算法
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Any
from prediction_algorithms import PredictionAlgorithms


class AdvancedPredictions(PredictionAlgorithms):
    """高级预测算法类，继承基础预测算法"""
    
    def predict_group_7_markov_big_cold(self, markov_probs: Dict[str, np.ndarray],
                                       historical_probs: Dict[str, pd.DataFrame],
                                       big_probs: Dict[str, pd.DataFrame],
                                       cold_probs: Dict[str, pd.DataFrame],
                                       current_db: pd.DataFrame,
                                       verbose: bool = True) -> Tuple[List[int], int]:
        """
        第7组预测：马尔科夫链+大球冷球历史出现概率筛选
        
        Args:
            markov_probs: 马尔科夫链概率
            historical_probs: 历史出现概率
            big_probs: 大球概率
            cold_probs: 冷球概率
            current_db: 当前数据库
            
        Returns:
            (红球列表, 蓝球)
        """
        # 获取第4组的大球要求
        red_big_prob = big_probs['red_big_prob']
        blue_big_prob = big_probs['blue_big_prob']
        required_red_big = red_big_prob.loc[red_big_prob['probability'].idxmax(), 'big_count']
        required_blue_big = blue_big_prob.loc[blue_big_prob['probability'].idxmax(), 'big_count']
        
        # 获取第5组的冷球要求
        red_cold_prob = cold_probs['red_cold_prob']
        blue_cold_prob = cold_probs['blue_cold_prob']
        required_red_cold = red_cold_prob.loc[red_cold_prob['probability'].idxmax(), 'cold_count']
        required_blue_cold = blue_cold_prob.loc[blue_cold_prob['probability'].idxmax(), 'cold_count']
        
        # 从第4组预测开始（已满足大球要求）
        initial_red, initial_blue = self.predict_group_4_markov_big(
            markov_probs, historical_probs, big_probs, verbose=verbose
        )
        
        # 获取冷球号码
        red_cold_balls = self.get_cold_balls(current_db, 'red')
        blue_cold_balls = self.get_cold_balls(current_db, 'blue')
        
        # 调整红球以满足冷球要求，同时保持大球要求
        red_balls = self._adjust_red_balls_for_big_and_cold(
            initial_red, required_red_big, required_red_cold, 
            red_cold_balls, historical_probs['red_number_prob']
        )
        
        # 调整蓝球以满足冷球要求，同时保持大球要求
        blue_ball = self._adjust_blue_ball_for_big_and_cold(
            initial_blue, required_blue_big, required_blue_cold,
            blue_cold_balls, historical_probs['blue_number_prob']
        )
        
        return red_balls, blue_ball
    
    def _adjust_red_balls_for_big_and_cold(self, red_balls: List[int], required_big: int,
                                          required_cold: int, cold_balls: List[int],
                                          red_prob: pd.DataFrame) -> List[int]:
        """
        调整红球以同时满足大球和冷球要求
        
        Args:
            red_balls: 初始红球列表
            required_big: 要求的大球数
            required_cold: 要求的冷球数
            cold_balls: 冷球号码列表
            red_prob: 红球概率表
            
        Returns:
            调整后的红球列表
        """
        prob_dict = dict(zip(red_prob['number'], red_prob['probability']))
        result_balls = red_balls.copy()
        
        # 当前状态
        current_big = self.core_algo.calculate_big_ball_count(result_balls, 'red')
        current_cold = len([ball for ball in result_balls if ball in cold_balls])
        
        # 如果已经满足要求，直接返回
        if current_big == required_big and current_cold == required_cold:
            return result_balls
        
        # 需要调整冷球数，同时保持大球数
        if current_cold != required_cold:
            if current_cold > required_cold:
                # 减少冷球
                cold_balls_in_result = [ball for ball in result_balls if ball in cold_balls]
                cold_balls_sorted = sorted(cold_balls_in_result, key=lambda x: prob_dict[x])
                
                to_remove = current_cold - required_cold
                for i in range(to_remove):
                    if i < len(cold_balls_sorted):
                        result_balls.remove(cold_balls_sorted[i])
                
                # 补充非冷球，保持大球数
                all_numbers = set(range(1, 34))
                excluded = set(result_balls)
                
                # 计算当前大球数
                current_big_after_remove = self.core_algo.calculate_big_ball_count(result_balls, 'red')
                need_big = required_big - current_big_after_remove
                
                if need_big > 0:
                    # 需要补充大球
                    big_non_cold = [ball for ball in all_numbers - excluded 
                                   if ball > 16 and ball not in cold_balls]
                    big_non_cold_sorted = sorted(big_non_cold, key=lambda x: prob_dict[x], reverse=True)
                    
                    for i in range(min(need_big, len(big_non_cold_sorted))):
                        if len(result_balls) < 6:
                            result_balls.append(big_non_cold_sorted[i])
                
                # 补充到6个球
                while len(result_balls) < 6:
                    excluded = set(result_balls)
                    remaining = [ball for ball in all_numbers - excluded if ball not in cold_balls]
                    if remaining:
                        remaining_sorted = sorted(remaining, key=lambda x: prob_dict[x], reverse=True)
                        result_balls.append(remaining_sorted[0])
                    else:
                        break
            
            else:
                # 增加冷球
                all_numbers = set(range(1, 34))
                excluded = set(result_balls)
                available_cold = [ball for ball in cold_balls if ball not in excluded]
                
                to_add = required_cold - current_cold
                
                # 优先选择满足大球要求的冷球
                current_big_count = self.core_algo.calculate_big_ball_count(result_balls, 'red')
                need_big = required_big - current_big_count
                
                if need_big > 0:
                    # 需要大球冷球
                    big_cold = [ball for ball in available_cold if ball > 16]
                    big_cold_sorted = sorted(big_cold, key=lambda x: prob_dict[x], reverse=True)
                    
                    for i in range(min(to_add, need_big, len(big_cold_sorted))):
                        result_balls.append(big_cold_sorted[i])
                        to_add -= 1
                
                # 补充剩余的冷球
                if to_add > 0:
                    excluded = set(result_balls)
                    remaining_cold = [ball for ball in cold_balls if ball not in excluded]
                    remaining_cold_sorted = sorted(remaining_cold, key=lambda x: prob_dict[x], reverse=True)
                    
                    for i in range(min(to_add, len(remaining_cold_sorted))):
                        if len(result_balls) < 6:
                            result_balls.append(remaining_cold_sorted[i])
                
                # 如果超过6个，移除概率最小的非冷球
                if len(result_balls) > 6:
                    non_cold_in_result = [ball for ball in result_balls if ball not in cold_balls]
                    non_cold_sorted = sorted(non_cold_in_result, key=lambda x: prob_dict[x])
                    
                    to_remove = len(result_balls) - 6
                    for i in range(to_remove):
                        if i < len(non_cold_sorted):
                            result_balls.remove(non_cold_sorted[i])
        
        return result_balls[:6]
    
    def _adjust_blue_ball_for_big_and_cold(self, blue_ball: int, required_big: int,
                                          required_cold: int, cold_balls: List[int],
                                          blue_prob: pd.DataFrame) -> int:
        """
        调整蓝球以同时满足大球和冷球要求
        
        Args:
            blue_ball: 初始蓝球
            required_big: 要求的大球数
            required_cold: 要求的冷球数
            cold_balls: 冷球号码列表
            blue_prob: 蓝球概率表
            
        Returns:
            调整后的蓝球
        """
        current_big = self.core_algo.calculate_big_ball_count([blue_ball], 'blue')
        current_cold = 1 if blue_ball in cold_balls else 0
        
        if current_big == required_big and current_cold == required_cold:
            return blue_ball
        
        prob_dict = dict(zip(blue_prob['number'], blue_prob['probability']))
        
        # 根据要求筛选候选球
        candidates = []
        
        for ball in range(1, 17):
            ball_big = self.core_algo.calculate_big_ball_count([ball], 'blue')
            ball_cold = 1 if ball in cold_balls else 0
            
            if ball_big == required_big and ball_cold == required_cold:
                candidates.append(ball)
        
        if candidates:
            # 选择概率最大的
            candidates_sorted = sorted(candidates, key=lambda x: prob_dict[x], reverse=True)
            return candidates_sorted[0]
        
        return blue_ball

    def predict_group_8_markov_big_repeat(self, markov_probs: Dict[str, np.ndarray],
                                         historical_probs: Dict[str, pd.DataFrame],
                                         big_probs: Dict[str, pd.DataFrame],
                                         repeat_prob: pd.DataFrame,
                                         latest_numbers: List[int],
                                         verbose: bool = True) -> Tuple[List[int], int]:
        """
        第8组预测：马尔科夫链+大球重号历史出现概率筛选
        """
        # 获取第4组的大球要求
        red_big_prob = big_probs['red_big_prob']
        required_red_big = red_big_prob.loc[red_big_prob['probability'].idxmax(), 'big_count']

        # 获取第6组的重号要求
        required_repeat = repeat_prob.loc[repeat_prob['probability'].idxmax(), 'repeat_count']

        # 从第4组预测开始（已满足大球要求）
        initial_red, initial_blue = self.predict_group_4_markov_big(
            markov_probs, historical_probs, big_probs, verbose=verbose
        )

        # 调整红球以满足重号要求，同时保持大球要求
        red_balls = self._adjust_red_balls_for_big_and_repeat(
            initial_red, required_red_big, required_repeat,
            latest_numbers[:6], historical_probs['red_number_prob']
        )

        return red_balls, initial_blue

    def _adjust_red_balls_for_big_and_repeat(self, red_balls: List[int], required_big: int,
                                            required_repeat: int, latest_red: List[int],
                                            red_prob: pd.DataFrame) -> List[int]:
        """调整红球以同时满足大球和重号要求"""
        prob_dict = dict(zip(red_prob['number'], red_prob['probability']))
        result_balls = red_balls.copy()

        current_big = self.core_algo.calculate_big_ball_count(result_balls, 'red')
        current_repeat = len(set(result_balls).intersection(set(latest_red)))

        if current_big == required_big and current_repeat == required_repeat:
            return result_balls

        # 需要调整重号数，同时保持大球数
        if current_repeat != required_repeat:
            if current_repeat > required_repeat:
                # 减少重号
                repeat_balls_in_result = [ball for ball in result_balls if ball in latest_red]
                repeat_balls_sorted = sorted(repeat_balls_in_result, key=lambda x: prob_dict[x])

                to_remove = current_repeat - required_repeat
                for i in range(to_remove):
                    if i < len(repeat_balls_sorted):
                        result_balls.remove(repeat_balls_sorted[i])

                # 补充非重号，保持大球数
                all_numbers = set(range(1, 34))
                excluded = set(result_balls)

                current_big_after_remove = self.core_algo.calculate_big_ball_count(result_balls, 'red')
                need_big = required_big - current_big_after_remove

                if need_big > 0:
                    big_non_repeat = [ball for ball in all_numbers - excluded
                                     if ball > 16 and ball not in latest_red]
                    big_non_repeat_sorted = sorted(big_non_repeat, key=lambda x: prob_dict[x], reverse=True)

                    for i in range(min(need_big, len(big_non_repeat_sorted))):
                        if len(result_balls) < 6:
                            result_balls.append(big_non_repeat_sorted[i])

                # 补充到6个球
                while len(result_balls) < 6:
                    excluded = set(result_balls)
                    remaining = [ball for ball in all_numbers - excluded if ball not in latest_red]
                    if remaining:
                        remaining_sorted = sorted(remaining, key=lambda x: prob_dict[x], reverse=True)
                        result_balls.append(remaining_sorted[0])
                    else:
                        break

            else:
                # 增加重号
                all_numbers = set(range(1, 34))
                excluded = set(result_balls)
                available_repeat = [ball for ball in latest_red if ball not in excluded]

                to_add = required_repeat - current_repeat

                # 优先选择满足大球要求的重号
                current_big_count = self.core_algo.calculate_big_ball_count(result_balls, 'red')
                need_big = required_big - current_big_count

                if need_big > 0:
                    big_repeat = [ball for ball in available_repeat if ball > 16]
                    big_repeat_sorted = sorted(big_repeat, key=lambda x: prob_dict[x], reverse=True)

                    for i in range(min(to_add, need_big, len(big_repeat_sorted))):
                        result_balls.append(big_repeat_sorted[i])
                        to_add -= 1

                # 补充剩余的重号
                if to_add > 0:
                    excluded = set(result_balls)
                    remaining_repeat = [ball for ball in latest_red if ball not in excluded]
                    remaining_repeat_sorted = sorted(remaining_repeat, key=lambda x: prob_dict[x], reverse=True)

                    for i in range(min(to_add, len(remaining_repeat_sorted))):
                        if len(result_balls) < 6:
                            result_balls.append(remaining_repeat_sorted[i])

                # 如果超过6个，移除概率最小的非重号
                if len(result_balls) > 6:
                    non_repeat_in_result = [ball for ball in result_balls if ball not in latest_red]
                    non_repeat_sorted = sorted(non_repeat_in_result, key=lambda x: prob_dict[x])

                    to_remove = len(result_balls) - 6
                    for i in range(to_remove):
                        if i < len(non_repeat_sorted):
                            result_balls.remove(non_repeat_sorted[i])

        return result_balls[:6]

    def predict_group_9_markov_cold_repeat(self, markov_probs: Dict[str, np.ndarray],
                                          historical_probs: Dict[str, pd.DataFrame],
                                          cold_probs: Dict[str, pd.DataFrame],
                                          repeat_prob: pd.DataFrame,
                                          current_db: pd.DataFrame,
                                          latest_numbers: List[int],
                                          verbose: bool = True) -> Tuple[List[int], int]:
        """第9组预测：马尔科夫链+冷球重号历史出现概率筛选"""
        # 获取第5组的冷球要求
        red_cold_prob = cold_probs['red_cold_prob']
        blue_cold_prob = cold_probs['blue_cold_prob']
        required_red_cold = red_cold_prob.loc[red_cold_prob['probability'].idxmax(), 'cold_count']
        required_blue_cold = blue_cold_prob.loc[blue_cold_prob['probability'].idxmax(), 'cold_count']

        # 获取第6组的重号要求
        required_repeat = repeat_prob.loc[repeat_prob['probability'].idxmax(), 'repeat_count']

        # 从第5组预测开始（已满足冷球要求）
        initial_red, initial_blue = self.predict_group_5_markov_cold(
            markov_probs, historical_probs, cold_probs, current_db, verbose=verbose
        )

        # 获取冷球号码
        red_cold_balls = self.get_cold_balls(current_db, 'red')

        # 调整红球以满足重号要求，同时保持冷球要求
        red_balls = self._adjust_red_balls_for_cold_and_repeat(
            initial_red, required_red_cold, required_repeat,
            red_cold_balls, latest_numbers[:6], historical_probs['red_number_prob']
        )

        return red_balls, initial_blue

    def _adjust_red_balls_for_cold_and_repeat(self, red_balls: List[int], required_cold: int,
                                             required_repeat: int, cold_balls: List[int],
                                             latest_red: List[int], red_prob: pd.DataFrame) -> List[int]:
        """调整红球以同时满足冷球和重号要求"""
        prob_dict = dict(zip(red_prob['number'], red_prob['probability']))
        result_balls = red_balls.copy()

        current_cold = len([ball for ball in result_balls if ball in cold_balls])
        current_repeat = len(set(result_balls).intersection(set(latest_red)))

        if current_cold == required_cold and current_repeat == required_repeat:
            return result_balls

        # 需要调整重号数，同时保持冷球数
        if current_repeat != required_repeat:
            if current_repeat > required_repeat:
                # 减少重号
                repeat_balls_in_result = [ball for ball in result_balls if ball in latest_red]
                repeat_balls_sorted = sorted(repeat_balls_in_result, key=lambda x: prob_dict[x])

                to_remove = current_repeat - required_repeat
                for i in range(to_remove):
                    if i < len(repeat_balls_sorted):
                        result_balls.remove(repeat_balls_sorted[i])

                # 补充非重号，保持冷球数
                all_numbers = set(range(1, 34))
                excluded = set(result_balls)

                current_cold_after_remove = len([ball for ball in result_balls if ball in cold_balls])
                need_cold = required_cold - current_cold_after_remove

                if need_cold > 0:
                    cold_non_repeat = [ball for ball in cold_balls if ball not in excluded and ball not in latest_red]
                    cold_non_repeat_sorted = sorted(cold_non_repeat, key=lambda x: prob_dict[x], reverse=True)

                    for i in range(min(need_cold, len(cold_non_repeat_sorted))):
                        if len(result_balls) < 6:
                            result_balls.append(cold_non_repeat_sorted[i])

                # 补充到6个球
                while len(result_balls) < 6:
                    excluded = set(result_balls)
                    remaining = [ball for ball in all_numbers - excluded if ball not in latest_red]
                    if remaining:
                        remaining_sorted = sorted(remaining, key=lambda x: prob_dict[x], reverse=True)
                        result_balls.append(remaining_sorted[0])
                    else:
                        break

            else:
                # 增加重号
                all_numbers = set(range(1, 34))
                excluded = set(result_balls)
                available_repeat = [ball for ball in latest_red if ball not in excluded]

                to_add = required_repeat - current_repeat

                # 优先选择满足冷球要求的重号
                current_cold_count = len([ball for ball in result_balls if ball in cold_balls])
                need_cold = required_cold - current_cold_count

                if need_cold > 0:
                    cold_repeat = [ball for ball in available_repeat if ball in cold_balls]
                    cold_repeat_sorted = sorted(cold_repeat, key=lambda x: prob_dict[x], reverse=True)

                    for i in range(min(to_add, need_cold, len(cold_repeat_sorted))):
                        result_balls.append(cold_repeat_sorted[i])
                        to_add -= 1

                # 补充剩余的重号
                if to_add > 0:
                    excluded = set(result_balls)
                    remaining_repeat = [ball for ball in latest_red if ball not in excluded]
                    remaining_repeat_sorted = sorted(remaining_repeat, key=lambda x: prob_dict[x], reverse=True)

                    for i in range(min(to_add, len(remaining_repeat_sorted))):
                        if len(result_balls) < 6:
                            result_balls.append(remaining_repeat_sorted[i])

                # 如果超过6个，移除概率最小的非重号
                if len(result_balls) > 6:
                    non_repeat_in_result = [ball for ball in result_balls if ball not in latest_red]
                    non_repeat_sorted = sorted(non_repeat_in_result, key=lambda x: prob_dict[x])

                    to_remove = len(result_balls) - 6
                    for i in range(to_remove):
                        if i < len(non_repeat_sorted):
                            result_balls.remove(non_repeat_sorted[i])

        return result_balls[:6]

    def predict_group_10_markov_big_cold_repeat(self, markov_probs: Dict[str, np.ndarray],
                                               historical_probs: Dict[str, pd.DataFrame],
                                               big_probs: Dict[str, pd.DataFrame],
                                               cold_probs: Dict[str, pd.DataFrame],
                                               repeat_prob: pd.DataFrame,
                                               current_db: pd.DataFrame,
                                               latest_numbers: List[int],
                                               verbose: bool = True) -> Tuple[List[int], int]:
        """第10组预测：马尔科夫链+大球冷球重号历史出现概率筛选"""
        # 获取所有要求
        red_big_prob = big_probs['red_big_prob']
        blue_big_prob = big_probs['blue_big_prob']
        required_red_big = red_big_prob.loc[red_big_prob['probability'].idxmax(), 'big_count']
        required_blue_big = blue_big_prob.loc[blue_big_prob['probability'].idxmax(), 'big_count']

        red_cold_prob = cold_probs['red_cold_prob']
        blue_cold_prob = cold_probs['blue_cold_prob']
        required_red_cold = red_cold_prob.loc[red_cold_prob['probability'].idxmax(), 'cold_count']
        required_blue_cold = blue_cold_prob.loc[blue_cold_prob['probability'].idxmax(), 'cold_count']

        required_repeat = repeat_prob.loc[repeat_prob['probability'].idxmax(), 'repeat_count']

        # 从第7组预测开始（已满足大球和冷球要求）
        initial_red, initial_blue = self.predict_group_7_markov_big_cold(
            markov_probs, historical_probs, big_probs, cold_probs, current_db, verbose=verbose
        )

        # 获取冷球号码
        red_cold_balls = self.get_cold_balls(current_db, 'red')

        # 调整红球以满足重号要求，同时保持大球和冷球要求
        red_balls = self._adjust_red_balls_for_all_requirements(
            initial_red, required_red_big, required_red_cold, required_repeat,
            red_cold_balls, latest_numbers[:6], historical_probs['red_number_prob']
        )

        return red_balls, initial_blue

    def _adjust_red_balls_for_all_requirements(self, red_balls: List[int], required_big: int,
                                              required_cold: int, required_repeat: int,
                                              cold_balls: List[int], latest_red: List[int],
                                              red_prob: pd.DataFrame) -> List[int]:
        """调整红球以同时满足大球、冷球和重号要求"""
        prob_dict = dict(zip(red_prob['number'], red_prob['probability']))
        result_balls = red_balls.copy()

        # 当前状态
        current_big = self.core_algo.calculate_big_ball_count(result_balls, 'red')
        current_cold = len([ball for ball in result_balls if ball in cold_balls])
        current_repeat = len(set(result_balls).intersection(set(latest_red)))

        # 如果已经满足所有要求，直接返回
        if (current_big == required_big and current_cold == required_cold and
            current_repeat == required_repeat):
            return result_balls

        # 复杂的调整逻辑，需要同时满足三个条件
        # 这里采用迭代优化的方法

        max_iterations = 10
        for iteration in range(max_iterations):
            # 重新计算当前状态
            current_big = self.core_algo.calculate_big_ball_count(result_balls, 'red')
            current_cold = len([ball for ball in result_balls if ball in cold_balls])
            current_repeat = len(set(result_balls).intersection(set(latest_red)))

            if (current_big == required_big and current_cold == required_cold and
                current_repeat == required_repeat):
                break

            # 找到需要调整的维度
            need_adjust_big = current_big != required_big
            need_adjust_cold = current_cold != required_cold
            need_adjust_repeat = current_repeat != required_repeat

            if need_adjust_repeat:
                # 优先调整重号数
                if current_repeat > required_repeat:
                    # 减少重号
                    repeat_balls_in_result = [ball for ball in result_balls if ball in latest_red]
                    # 选择一个不影响其他要求的重号球移除
                    for ball in repeat_balls_in_result:
                        temp_result = [b for b in result_balls if b != ball]
                        temp_big = self.core_algo.calculate_big_ball_count(temp_result, 'red')
                        temp_cold = len([b for b in temp_result if b in cold_balls])

                        # 如果移除这个球不会让其他指标变得更差，就移除它
                        if ((not need_adjust_big) or
                            (required_big > current_big and temp_big >= current_big) or
                            (required_big < current_big and temp_big <= current_big) or
                            (required_big == current_big and temp_big == current_big)):
                            if ((not need_adjust_cold) or
                                (required_cold > current_cold and temp_cold >= current_cold) or
                                (required_cold < current_cold and temp_cold <= current_cold) or
                                (required_cold == current_cold and temp_cold == current_cold)):
                                result_balls.remove(ball)
                                break

                elif current_repeat < required_repeat:
                    # 增加重号
                    all_numbers = set(range(1, 34))
                    excluded = set(result_balls)
                    available_repeat = [ball for ball in latest_red if ball not in excluded]

                    # 选择一个满足其他要求的重号球添加
                    for ball in available_repeat:
                        temp_result = result_balls + [ball]
                        if len(temp_result) <= 6:
                            temp_big = self.core_algo.calculate_big_ball_count(temp_result, 'red')
                            temp_cold = len([b for b in temp_result if b in cold_balls])

                            # 检查是否改善了其他指标
                            big_ok = (not need_adjust_big) or (
                                (required_big > current_big and temp_big > current_big) or
                                (required_big < current_big and temp_big < current_big) or
                                (required_big == current_big)
                            )
                            cold_ok = (not need_adjust_cold) or (
                                (required_cold > current_cold and temp_cold > current_cold) or
                                (required_cold < current_cold and temp_cold < current_cold) or
                                (required_cold == current_cold)
                            )

                            if big_ok and cold_ok:
                                result_balls.append(ball)
                                break

            # 如果还需要调整其他维度，进行微调
            if len(result_balls) < 6:
                # 补充球到6个
                all_numbers = set(range(1, 34))
                excluded = set(result_balls)
                candidates = list(all_numbers - excluded)

                # 按照满足要求的程度排序候选球
                candidate_scores = []
                for ball in candidates:
                    temp_result = result_balls + [ball]
                    temp_big = self.core_algo.calculate_big_ball_count(temp_result, 'red')
                    temp_cold = len([b for b in temp_result if b in cold_balls])
                    temp_repeat = len(set(temp_result).intersection(set(latest_red)))

                    # 计算与目标的距离
                    big_diff = abs(temp_big - required_big)
                    cold_diff = abs(temp_cold - required_cold)
                    repeat_diff = abs(temp_repeat - required_repeat)

                    total_diff = big_diff + cold_diff + repeat_diff
                    candidate_scores.append((total_diff, prob_dict[ball], ball))

                # 选择最佳候选
                candidate_scores.sort(key=lambda x: (x[0], -x[1]))

                for _, _, ball in candidate_scores:
                    if len(result_balls) < 6:
                        result_balls.append(ball)
                    else:
                        break

            elif len(result_balls) > 6:
                # 移除多余的球
                # 选择移除后对目标影响最小的球
                removal_scores = []
                for ball in result_balls:
                    temp_result = [b for b in result_balls if b != ball]
                    temp_big = self.core_algo.calculate_big_ball_count(temp_result, 'red')
                    temp_cold = len([b for b in temp_result if b in cold_balls])
                    temp_repeat = len(set(temp_result).intersection(set(latest_red)))

                    big_diff = abs(temp_big - required_big)
                    cold_diff = abs(temp_cold - required_cold)
                    repeat_diff = abs(temp_repeat - required_repeat)

                    total_diff = big_diff + cold_diff + repeat_diff
                    removal_scores.append((total_diff, -prob_dict[ball], ball))

                removal_scores.sort(key=lambda x: (x[0], x[1]))

                while len(result_balls) > 6 and removal_scores:
                    _, _, ball_to_remove = removal_scores.pop(0)
                    if ball_to_remove in result_balls:
                        result_balls.remove(ball_to_remove)

        return result_balls[:6]
