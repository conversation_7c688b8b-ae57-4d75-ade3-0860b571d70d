"""
测试修复后的功能
"""

from main import LotteryPredictionSystem


def test_prediction_format_fix():
    """测试第3组预测蓝球号码格式修复"""
    print("=== 测试第3组预测蓝球号码格式修复 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 设置测试参数
    periods = 500
    system.current_db = system.data_loader.get_current_database(periods)
    
    # 获取最新期信息
    latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
    
    # 计算历史概率
    historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
    
    # 第3组预测
    red3, blue3 = system.prediction_algo.predict_group_3_historical(historical_probs)
    
    print(f"第3组预测结果:")
    print(f"红球: {red3} (类型: {type(red3[0])})")
    print(f"蓝球: {blue3} (类型: {type(blue3)})")
    
    # 验证蓝球是否为整数
    if isinstance(blue3, int):
        print("✅ 蓝球号码格式修复成功！")
    else:
        print("❌ 蓝球号码格式仍有问题")
    
    # 打印格式化结果
    system.ui.print_prediction_result(3, red3, blue3)


def test_analysis_fix():
    """测试分析比对功能修复"""
    print("\n=== 测试分析比对功能修复 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 选择测试期号
    target_period = "25065"
    target_period_int = int(target_period)
    periods = 500
    
    print(f"测试期号: {target_period}")
    
    # 进行1期分析测试
    all_results = []
    
    # 更新当前数据库
    current_target_idx = system.raw_data[system.raw_data['NO'] == target_period_int].index[0]
    start_idx = max(0, current_target_idx + 1 - periods)
    system.current_db = system.raw_data.iloc[start_idx:current_target_idx + 1].copy()
    
    # 获取答案数据
    answer_data = system.data_loader.get_answer_data(target_period_int, 6)
    if answer_data is None:
        print("数据不足，无法进行测试")
        return
    
    # 进行预测
    try:
        # 获取最新期信息
        latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
        
        # 计算必要的概率
        historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
        cold_probs = system.core_algo.calculate_cold_ball_probability(system.current_db)
        repeat_prob = system.core_algo.calculate_repeat_probability(system.current_db)
        follow_matrices = system.core_algo.calculate_following_probability(system.current_db)
        markov_probs = system.core_algo.calculate_markov_chain(
            latest_numbers, follow_matrices, historical_probs
        )
        bayesian_probs = system.core_algo.calculate_bayesian_probability(
            latest_numbers, follow_matrices, historical_probs
        )
        
        # 预测所有10组
        predictions = []
        
        # 第1组：马尔科夫链
        red1, blue1 = system.prediction_algo.predict_group_1_markov(markov_probs)
        predictions.append(red1 + [blue1])
        
        # 第2组：贝叶斯概率
        red2, blue2 = system.prediction_algo.predict_group_2_bayesian(bayesian_probs)
        predictions.append(red2 + [blue2])
        
        # 第3组：历史出现概率
        red3, blue3 = system.prediction_algo.predict_group_3_historical(historical_probs)
        predictions.append(red3 + [blue3])
        
        # 第4组：马尔科夫链+大球筛选
        red4, blue4 = system.prediction_algo.predict_group_4_markov_big(
            markov_probs, historical_probs, historical_probs
        )
        predictions.append(red4 + [blue4])
        
        # 第5组：马尔科夫链+冷球筛选
        red5, blue5 = system.prediction_algo.predict_group_5_markov_cold(
            markov_probs, historical_probs, cold_probs, system.current_db
        )
        predictions.append(red5 + [blue5])
        
        # 第6组：马尔科夫链+重号筛选
        red6, blue6 = system.prediction_algo.predict_group_6_markov_repeat(
            markov_probs, historical_probs, repeat_prob, latest_numbers
        )
        predictions.append(red6 + [blue6])
        
        # 第7组：马尔科夫链+大球冷球筛选
        red7, blue7 = system.advanced_pred.predict_group_7_markov_big_cold(
            markov_probs, historical_probs, historical_probs, cold_probs, system.current_db
        )
        predictions.append(red7 + [blue7])
        
        # 第8组：马尔科夫链+大球重号筛选
        red8, blue8 = system.advanced_pred.predict_group_8_markov_big_repeat(
            markov_probs, historical_probs, historical_probs, repeat_prob, latest_numbers
        )
        predictions.append(red8 + [blue8])
        
        # 第9组：马尔科夫链+冷球重号筛选
        red9, blue9 = system.advanced_pred.predict_group_9_markov_cold_repeat(
            markov_probs, historical_probs, cold_probs, repeat_prob, system.current_db, latest_numbers
        )
        predictions.append(red9 + [blue9])
        
        # 第10组：马尔科夫链+大球冷球重号筛选
        red10, blue10 = system.advanced_pred.predict_group_10_markov_big_cold_repeat(
            markov_probs, historical_probs, historical_probs, cold_probs, repeat_prob, 
            system.current_db, latest_numbers
        )
        predictions.append(red10 + [blue10])
        
        print(f"✅ 成功生成 {len(predictions)} 组预测号码")
        
        # 准备答案数据
        answers = []
        for _, row in answer_data.iterrows():
            answer = [row['r1'], row['r2'], row['r3'], row['r4'], row['r5'], row['r6'], row['b']]
            answers.append(answer)
        
        # 比对
        comparison_result = system.comparison_algo.compare_predictions_with_answers(predictions, answers)
        
        # 格式化结果
        formatted_result = system.comparison_algo.format_comparison_result(
            str(target_period_int), predictions, answers, comparison_result
        )
        
        all_results.append(formatted_result)
        
        # 打印比对结果
        print(f"\n期号 {target_period_int} 比对结果:")
        for group_name, group_result in comparison_result.items():
            pred = group_result['prediction']
            red_str = ' '.join(map(str, sorted(pred[:6])))
            print(f"  {group_name}: {red_str} + {pred[6]} -> 最大命中 {group_result['max_hits']} 球")
        
        # 保存结果
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"修复测试结果_{target_period}_{timestamp}.xlsx"
        system.comparison_algo.save_comparison_results(all_results, output_file)
        
        # 分析命中分布
        distribution = system.comparison_algo.analyze_hit_distribution(all_results)
        print(f"\n命中分布统计:")
        for group_name, group_dist in distribution.items():
            total = sum(group_dist.values())
            print(f"{group_name}: 总计 {total} 期")
        
        print(f"\n✅ 分析比对功能修复测试完成！结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("双色球预测程序修复测试")
    print("=" * 50)
    
    # 测试第3组预测格式修复
    test_prediction_format_fix()
    
    # 测试分析比对功能修复
    test_analysis_fix()
    
    print("\n" + "=" * 50)
    print("修复测试完成！")


if __name__ == "__main__":
    main()
