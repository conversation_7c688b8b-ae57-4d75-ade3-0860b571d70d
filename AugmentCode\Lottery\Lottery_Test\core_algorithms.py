"""
核心算法模块
包含双色球预测的核心算法：统计概率、马尔科夫链、贝叶斯概率等
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any


class CoreAlgorithms:
    """核心算法类"""
    
    def __init__(self):
        """初始化核心算法"""
        pass
    
    def calculate_big_ball_count(self, numbers: List[int], ball_type: str) -> int:
        """
        计算大球数
        
        Args:
            numbers: 号码列表
            ball_type: 球类型 'red' 或 'blue'
            
        Returns:
            大球数量
        """
        if ball_type == 'red':
            # 红球大球：号码 > 16
            return sum(1 for num in numbers if num > 16)
        elif ball_type == 'blue':
            # 蓝球大球：号码 > 8
            return sum(1 for num in numbers if num > 8)
        else:
            raise ValueError("ball_type must be 'red' or 'blue'")
    
    def calculate_cold_ball_count(self, current_numbers: List[int], 
                                previous_5_periods: List[List[int]], 
                                ball_type: str) -> int:
        """
        计算冷球数
        
        Args:
            current_numbers: 当前期号码
            previous_5_periods: 前5期的号码列表
            ball_type: 球类型 'red' 或 'blue'
            
        Returns:
            冷球数量
        """
        # 获取前5期所有出现的号码
        previous_numbers = set()
        for period_numbers in previous_5_periods:
            if ball_type == 'red':
                previous_numbers.update(period_numbers)
            else:  # blue
                previous_numbers.add(period_numbers[0] if isinstance(period_numbers, list) else period_numbers)
        
        # 计算当前期中未在前5期出现的号码数量
        cold_count = 0
        if ball_type == 'red':
            for num in current_numbers:
                if num not in previous_numbers:
                    cold_count += 1
        else:  # blue
            current_blue = current_numbers[0] if isinstance(current_numbers, list) else current_numbers
            if current_blue not in previous_numbers:
                cold_count = 1
                
        return cold_count
    
    def calculate_repeat_count(self, current_red: List[int], previous_red: List[int]) -> int:
        """
        计算红球重号数
        
        Args:
            current_red: 当前期红球号码
            previous_red: 上一期红球号码
            
        Returns:
            重号数量
        """
        current_set = set(current_red)
        previous_set = set(previous_red)
        return len(current_set.intersection(previous_set))
    
    def calculate_historical_probability(self, current_db: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        计算历史出现概率
        
        Args:
            current_db: 当前数据库
            
        Returns:
            包含各种概率表的字典
        """
        results = {}
        
        # 1. 红球号码历史出现概率
        red_columns = ['r1', 'r2', 'r3', 'r4', 'r5', 'r6']
        all_red_numbers = []
        for col in red_columns:
            all_red_numbers.extend(current_db[col].tolist())
        
        red_counts = pd.Series(all_red_numbers).value_counts().sort_index()
        red_total = len(all_red_numbers)
        red_prob = pd.DataFrame({
            'number': range(1, 34),
            'probability': [red_counts.get(i, 0) / red_total for i in range(1, 34)]
        })
        results['red_number_prob'] = red_prob
        
        # 2. 蓝球号码历史出现概率
        blue_counts = current_db['b'].value_counts().sort_index()
        blue_total = len(current_db)
        blue_prob = pd.DataFrame({
            'number': range(1, 17),
            'probability': [blue_counts.get(i, 0) / blue_total for i in range(1, 17)]
        })
        results['blue_number_prob'] = blue_prob
        
        # 3. 红球大球数历史出现概率
        red_big_counts = []
        for _, row in current_db.iterrows():
            red_numbers = [row['r1'], row['r2'], row['r3'], row['r4'], row['r5'], row['r6']]
            big_count = self.calculate_big_ball_count(red_numbers, 'red')
            red_big_counts.append(big_count)
        
        red_big_series = pd.Series(red_big_counts).value_counts().sort_index()
        red_big_prob = pd.DataFrame({
            'big_count': range(0, 7),
            'probability': [red_big_series.get(i, 0) / len(red_big_counts) for i in range(0, 7)]
        })
        results['red_big_prob'] = red_big_prob
        
        # 4. 蓝球大球数历史出现概率
        blue_big_counts = []
        for _, row in current_db.iterrows():
            big_count = self.calculate_big_ball_count([row['b']], 'blue')
            blue_big_counts.append(big_count)
        
        blue_big_series = pd.Series(blue_big_counts).value_counts().sort_index()
        blue_big_prob = pd.DataFrame({
            'big_count': range(0, 2),
            'probability': [blue_big_series.get(i, 0) / len(blue_big_counts) for i in range(0, 2)]
        })
        results['blue_big_prob'] = blue_big_prob
        
        return results
    
    def calculate_cold_ball_probability(self, current_db: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        计算冷球数历史出现概率
        
        Args:
            current_db: 当前数据库
            
        Returns:
            冷球概率表字典
        """
        results = {}
        
        # 计算每期的冷球数
        red_cold_counts = []
        blue_cold_counts = []
        
        for i in range(5, len(current_db)):  # 从第6期开始计算（需要前5期数据）
            current_row = current_db.iloc[i]
            previous_5_rows = current_db.iloc[i-5:i]
            
            # 当前期红球
            current_red = [current_row['r1'], current_row['r2'], current_row['r3'], 
                          current_row['r4'], current_row['r5'], current_row['r6']]
            
            # 前5期红球
            previous_red_periods = []
            for _, prev_row in previous_5_rows.iterrows():
                prev_red = [prev_row['r1'], prev_row['r2'], prev_row['r3'], 
                           prev_row['r4'], prev_row['r5'], prev_row['r6']]
                previous_red_periods.append(prev_red)
            
            # 计算红球冷球数
            red_cold = self.calculate_cold_ball_count(current_red, previous_red_periods, 'red')
            red_cold_counts.append(red_cold)
            
            # 计算蓝球冷球数
            current_blue = current_row['b']
            previous_blue = [prev_row['b'] for _, prev_row in previous_5_rows.iterrows()]
            blue_cold = self.calculate_cold_ball_count([current_blue], previous_blue, 'blue')
            blue_cold_counts.append(blue_cold)
        
        # 红球冷球数概率
        red_cold_series = pd.Series(red_cold_counts).value_counts().sort_index()
        red_cold_prob = pd.DataFrame({
            'cold_count': range(0, 7),
            'probability': [red_cold_series.get(i, 0) / len(red_cold_counts) for i in range(0, 7)]
        })
        results['red_cold_prob'] = red_cold_prob
        
        # 蓝球冷球数概率
        blue_cold_series = pd.Series(blue_cold_counts).value_counts().sort_index()
        blue_cold_prob = pd.DataFrame({
            'cold_count': range(0, 2),
            'probability': [blue_cold_series.get(i, 0) / len(blue_cold_counts) for i in range(0, 2)]
        })
        results['blue_cold_prob'] = blue_cold_prob
        
        return results
    
    def calculate_repeat_probability(self, current_db: pd.DataFrame) -> pd.DataFrame:
        """
        计算红球重号数历史出现概率
        
        Args:
            current_db: 当前数据库
            
        Returns:
            重号概率表
        """
        repeat_counts = []
        
        for i in range(1, len(current_db)):  # 从第2期开始计算
            current_row = current_db.iloc[i]
            previous_row = current_db.iloc[i-1]
            
            current_red = [current_row['r1'], current_row['r2'], current_row['r3'], 
                          current_row['r4'], current_row['r5'], current_row['r6']]
            previous_red = [previous_row['r1'], previous_row['r2'], previous_row['r3'], 
                           previous_row['r4'], previous_row['r5'], previous_row['r6']]
            
            repeat_count = self.calculate_repeat_count(current_red, previous_red)
            repeat_counts.append(repeat_count)
        
        repeat_series = pd.Series(repeat_counts).value_counts().sort_index()
        repeat_prob = pd.DataFrame({
            'repeat_count': range(0, 7),
            'probability': [repeat_series.get(i, 0) / len(repeat_counts) for i in range(0, 7)]
        })
        
        return repeat_prob

    def calculate_following_probability(self, current_db: pd.DataFrame) -> Dict[str, np.ndarray]:
        """
        计算历史跟随性概率矩阵

        Args:
            current_db: 当前数据库

        Returns:
            包含红球和蓝球跟随性概率矩阵的字典
        """
        results = {}

        # 红球跟随性概率矩阵 (33x33)
        red_follow_matrix = np.zeros((33, 33))
        red_follow_counts = np.zeros((33, 33))

        # 蓝球跟随性概率矩阵 (16x16)
        blue_follow_matrix = np.zeros((16, 16))
        blue_follow_counts = np.zeros((16, 16))

        # 统计相邻两期的跟随关系
        for i in range(len(current_db) - 1):
            current_row = current_db.iloc[i]
            next_row = current_db.iloc[i + 1]

            # 当前期红球
            current_red = [current_row['r1'], current_row['r2'], current_row['r3'],
                          current_row['r4'], current_row['r5'], current_row['r6']]

            # 下一期红球
            next_red = [next_row['r1'], next_row['r2'], next_row['r3'],
                       next_row['r4'], next_row['r5'], next_row['r6']]

            # 统计红球跟随关系
            for curr_ball in current_red:
                for next_ball in next_red:
                    red_follow_counts[curr_ball-1, next_ball-1] += 1

            # 统计蓝球跟随关系
            curr_blue = current_row['b']
            next_blue = next_row['b']
            blue_follow_counts[curr_blue-1, next_blue-1] += 1

        # 转换为概率矩阵
        for i in range(33):
            total = np.sum(red_follow_counts[i, :])
            if total > 0:
                red_follow_matrix[i, :] = red_follow_counts[i, :] / total

        for i in range(16):
            total = np.sum(blue_follow_counts[i, :])
            if total > 0:
                blue_follow_matrix[i, :] = blue_follow_counts[i, :] / total

        results['red_follow_matrix'] = red_follow_matrix
        results['blue_follow_matrix'] = blue_follow_matrix

        return results

    def calculate_markov_chain(self, latest_numbers: List[int],
                              follow_matrices: Dict[str, np.ndarray],
                              historical_probs: Dict[str, pd.DataFrame]) -> Dict[str, np.ndarray]:
        """
        计算马尔科夫链概率

        Args:
            latest_numbers: 最新一期号码 [红球1-6, 蓝球]
            follow_matrices: 跟随性概率矩阵
            historical_probs: 历史出现概率

        Returns:
            马尔科夫链概率向量字典
        """
        results = {}

        # 红球马尔科夫链
        red_numbers = latest_numbers[:6]  # 前6个是红球
        red_follow_matrix = follow_matrices['red_follow_matrix']
        red_hist_prob = historical_probs['red_number_prob']['probability'].values

        # 提取相关列并组成新矩阵
        selected_columns = []
        for red_num in red_numbers:
            selected_columns.append(red_follow_matrix[:, red_num-1])

        new_matrix = np.column_stack(selected_columns)  # 33x6矩阵

        # 提取相关行组成新向量
        selected_probs = []
        for red_num in red_numbers:
            selected_probs.append(red_hist_prob[red_num-1])

        new_vector = np.array(selected_probs)  # 6x1向量

        # 矩阵乘法
        red_markov = np.dot(new_matrix, new_vector)  # 33x1向量
        results['red_markov'] = red_markov

        # 蓝球马尔科夫链
        blue_number = latest_numbers[6]  # 第7个是蓝球
        blue_follow_matrix = follow_matrices['blue_follow_matrix']

        # 直接使用对应列作为蓝球马尔科夫链向量
        blue_markov = blue_follow_matrix[:, blue_number-1]  # 16x1向量
        results['blue_markov'] = blue_markov

        return results

    def calculate_bayesian_probability(self, latest_numbers: List[int],
                                     follow_matrices: Dict[str, np.ndarray],
                                     historical_probs: Dict[str, pd.DataFrame]) -> Dict[str, np.ndarray]:
        """
        计算贝叶斯概率

        Args:
            latest_numbers: 最新一期号码 [红球1-6, 蓝球]
            follow_matrices: 跟随性概率矩阵
            historical_probs: 历史出现概率

        Returns:
            贝叶斯概率向量字典
        """
        results = {}

        # 红球贝叶斯概率
        red_numbers = latest_numbers[:6]
        red_follow_matrix = follow_matrices['red_follow_matrix']
        red_hist_prob = historical_probs['red_number_prob']['probability'].values

        # 提取相关行并求和
        selected_rows = []
        for red_num in red_numbers:
            selected_rows.append(red_follow_matrix[red_num-1, :])

        sum_vector = np.sum(selected_rows, axis=0)  # 1x33向量

        # 元素级乘法
        red_bayesian = sum_vector * red_hist_prob  # 33x1向量
        results['red_bayesian'] = red_bayesian

        # 蓝球贝叶斯概率
        blue_number = latest_numbers[6]
        blue_follow_matrix = follow_matrices['blue_follow_matrix']
        blue_hist_prob = historical_probs['blue_number_prob']['probability'].values

        # 提取相关行
        blue_row = blue_follow_matrix[blue_number-1, :]  # 1x16向量

        # 元素级乘法
        blue_bayesian = blue_row * blue_hist_prob  # 16x1向量
        results['blue_bayesian'] = blue_bayesian

        return results
