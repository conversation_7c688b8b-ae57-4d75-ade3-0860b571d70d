"""
测试完整预测功能的脚本
"""

from main import LotteryPredictionSystem


def test_full_prediction():
    """测试完整预测功能"""
    try:
        # 创建系统实例
        system = LotteryPredictionSystem()
        
        # 初始化数据
        print("正在初始化数据...")
        if not system.initialize_data():
            print("数据初始化失败")
            return
        
        # 设置测试参数
        periods = 500  # 使用最新500期数据
        system.current_db = system.data_loader.get_current_database(periods)
        
        # 获取最新期信息
        latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
        
        print(f"当前数据库包含: {len(system.current_db)} 期数据")
        red_str = ' '.join(map(str, sorted(latest_numbers[:6])))
        blue_str = str(latest_numbers[6])
        print(f"最新一期的号码为：{latest_period} {red_str} + {blue_str}")
        
        # 计算各种概率
        print("\n正在计算概率...")
        historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
        cold_probs = system.core_algo.calculate_cold_ball_probability(system.current_db)
        repeat_prob = system.core_algo.calculate_repeat_probability(system.current_db)
        follow_matrices = system.core_algo.calculate_following_probability(system.current_db)
        markov_probs = system.core_algo.calculate_markov_chain(
            latest_numbers, follow_matrices, historical_probs
        )
        bayesian_probs = system.core_algo.calculate_bayesian_probability(
            latest_numbers, follow_matrices, historical_probs
        )
        
        # 打印要求信息
        red_big_req = historical_probs['red_big_prob'].loc[
            historical_probs['red_big_prob']['probability'].idxmax(), 'big_count'
        ]
        blue_big_req = historical_probs['blue_big_prob'].loc[
            historical_probs['blue_big_prob']['probability'].idxmax(), 'big_count'
        ]
        red_cold_req = cold_probs['red_cold_prob'].loc[
            cold_probs['red_cold_prob']['probability'].idxmax(), 'cold_count'
        ]
        blue_cold_req = cold_probs['blue_cold_prob'].loc[
            cold_probs['blue_cold_prob']['probability'].idxmax(), 'cold_count'
        ]
        red_repeat_req = repeat_prob.loc[
            repeat_prob['probability'].idxmax(), 'repeat_count'
        ]
        
        system.ui.print_prediction_requirements(
            red_big_req, blue_big_req, red_cold_req, blue_cold_req, red_repeat_req
        )
        
        # 获取冷球信息
        red_cold_balls = system.prediction_algo.get_cold_balls(system.current_db, 'red')
        blue_cold_balls = system.prediction_algo.get_cold_balls(system.current_db, 'blue')
        system.ui.print_cold_balls_info(red_cold_balls, blue_cold_balls)
        
        # 进行所有10组预测
        print("\n=== 预测结果 ===")
        predictions = []
        
        # 第1组：马尔科夫链
        red1, blue1 = system.prediction_algo.predict_group_1_markov(markov_probs)
        system.ui.print_prediction_result(1, red1, blue1)
        predictions.append((red1, blue1))
        
        # 第2组：贝叶斯概率
        red2, blue2 = system.prediction_algo.predict_group_2_bayesian(bayesian_probs)
        system.ui.print_prediction_result(2, red2, blue2)
        predictions.append((red2, blue2))
        
        # 第3组：历史出现概率
        red3, blue3 = system.prediction_algo.predict_group_3_historical(historical_probs)
        system.ui.print_prediction_result(3, red3, blue3)
        predictions.append((red3, blue3))
        
        # 第4组：马尔科夫链+大球筛选
        red4, blue4 = system.prediction_algo.predict_group_4_markov_big(
            markov_probs, historical_probs, historical_probs
        )
        system.ui.print_prediction_result(4, red4, blue4)
        predictions.append((red4, blue4))
        
        # 第5组：马尔科夫链+冷球筛选
        red5, blue5 = system.prediction_algo.predict_group_5_markov_cold(
            markov_probs, historical_probs, cold_probs, system.current_db
        )
        system.ui.print_prediction_result(5, red5, blue5)
        predictions.append((red5, blue5))
        
        # 第6组：马尔科夫链+重号筛选
        red6, blue6 = system.prediction_algo.predict_group_6_markov_repeat(
            markov_probs, historical_probs, repeat_prob, latest_numbers
        )
        system.ui.print_prediction_result(6, red6, blue6)
        predictions.append((red6, blue6))
        
        # 第7组：马尔科夫链+大球冷球筛选
        red7, blue7 = system.advanced_pred.predict_group_7_markov_big_cold(
            markov_probs, historical_probs, historical_probs, cold_probs, system.current_db
        )
        system.ui.print_prediction_result(7, red7, blue7)
        predictions.append((red7, blue7))
        
        # 第8组：马尔科夫链+大球重号筛选
        red8, blue8 = system.advanced_pred.predict_group_8_markov_big_repeat(
            markov_probs, historical_probs, historical_probs, repeat_prob, latest_numbers
        )
        system.ui.print_prediction_result(8, red8, blue8)
        predictions.append((red8, blue8))
        
        # 第9组：马尔科夫链+冷球重号筛选
        red9, blue9 = system.advanced_pred.predict_group_9_markov_cold_repeat(
            markov_probs, historical_probs, cold_probs, repeat_prob, system.current_db, latest_numbers
        )
        system.ui.print_prediction_result(9, red9, blue9)
        predictions.append((red9, blue9))
        
        # 第10组：马尔科夫链+大球冷球重号筛选
        red10, blue10 = system.advanced_pred.predict_group_10_markov_big_cold_repeat(
            markov_probs, historical_probs, historical_probs, cold_probs, repeat_prob, 
            system.current_db, latest_numbers
        )
        system.ui.print_prediction_result(10, red10, blue10)
        predictions.append((red10, blue10))
        
        print(f"\n测试完成！所有10组预测算法运行正常。")
        print(f"共生成 {len(predictions)} 组预测号码。")
        
        # 保存预测结果
        all_tables = {
            'historical_probs': historical_probs,
            'cold_probs': cold_probs,
            'repeat_prob': repeat_prob,
            'follow_matrices': follow_matrices,
            'markov_probs': markov_probs,
            'bayesian_probs': bayesian_probs,
            'predictions': predictions
        }
        
        filename = system.ui.save_prediction_tables(all_tables, len(system.current_db))
        if filename:
            print(f"预测结果和统计表格已保存到: {filename}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_full_prediction()
