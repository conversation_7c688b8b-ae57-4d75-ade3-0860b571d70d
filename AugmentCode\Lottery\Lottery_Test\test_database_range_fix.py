"""
测试数据库范围修复
"""

from main import LotteryPredictionSystem


def test_database_range_fix():
    """测试数据库范围修复"""
    print("=== 测试数据库范围修复 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 模拟用户输入
    target_period = "20001"  # 用户输入的目标期号
    periods = 571  # 用户输入的数据库范围
    
    print(f"目标期号: {target_period}")
    print(f"数据库范围: {periods} 期")
    
    # 获取可用期号列表
    available_periods = sorted([int(period) for period in system.raw_data['NO'].tolist()])
    
    # 检查目标期号是否存在
    target_period_int = int(target_period)
    if target_period_int not in available_periods:
        print(f"目标期号 {target_period_int} 不存在")
        return
    
    # 找到目标期号在列表中的位置
    start_idx = available_periods.index(target_period_int)
    
    # 计算可分析期数
    target_idx = system.raw_data[system.raw_data['NO'] == target_period_int].index[0]
    max_possible_periods = len(system.raw_data) - target_idx - 6
    
    print(f"目标期号在列表中的索引: {start_idx}")
    print(f"目标期号在数据中的索引: {target_idx}")
    print(f"最大可分析期数: {max_possible_periods}")
    
    # 测试前55期（包含第50期的进度显示）
    test_periods = min(55, max_possible_periods)
    print(f"将测试 {test_periods} 期")
    
    for i in range(test_periods):
        # 使用期号列表进行正确的跳转
        if start_idx + i >= len(available_periods):
            print("已到达数据末尾")
            break
            
        current_target = available_periods[start_idx + i]
        
        # 更新当前数据库（修复后的逻辑）
        current_target_idx = system.raw_data[system.raw_data['NO'] == current_target].index[0]
        start_db_idx = max(0, current_target_idx + 1 - periods)
        system.current_db = system.raw_data.iloc[start_db_idx:current_target_idx + 1].copy()
        
        # 验证数据库大小
        expected_db_size = min(periods, current_target_idx + 1)
        actual_db_size = len(system.current_db)
        
        if i == 0:  # 第一期
            print(f"\n第1期验证:")
            print(f"  当前分析期号: {current_target}")
            print(f"  期望数据库大小: {expected_db_size}")
            print(f"  实际数据库大小: {actual_db_size}")
            print(f"  数据库大小正确: {'✅' if actual_db_size == expected_db_size else '❌'}")
            
            # 验证数据库内容
            db_first_period = system.current_db.iloc[0]['NO']
            db_last_period = system.current_db.iloc[-1]['NO']
            print(f"  数据库期号范围: {int(db_first_period)} - {int(db_last_period)}")
            print(f"  数据库最后期号正确: {'✅' if int(db_last_period) == current_target else '❌'}")
        
        if i == 49:  # 第50期
            print(f"\n第50期验证:")
            print(f"  当前分析期号: {current_target}")
            print(f"  期望数据库大小: {expected_db_size}")
            print(f"  实际数据库大小: {actual_db_size}")
            print(f"  数据库大小正确: {'✅' if actual_db_size == expected_db_size else '❌'}")
            
            # 验证数据库内容
            db_first_period = system.current_db.iloc[0]['NO']
            db_last_period = system.current_db.iloc[-1]['NO']
            print(f"  数据库期号范围: {int(db_first_period)} - {int(db_last_period)}")
            print(f"  数据库最后期号正确: {'✅' if int(db_last_period) == current_target else '❌'}")
            
            # 获取当前分析目标期号的信息
            current_target_row = system.raw_data[system.raw_data['NO'] == current_target].iloc[0]
            current_target_numbers = [
                current_target_row['r1'], current_target_row['r2'], current_target_row['r3'],
                current_target_row['r4'], current_target_row['r5'], current_target_row['r6'],
                current_target_row['b']
            ]
            
            print(f"  当前分析期号码: {' '.join(map(str, current_target_numbers[:6]))} + {current_target_numbers[6]}")
            
            # 验证期号是否正确（应该是20001 + 49 = 20050）
            expected_period = target_period_int + 49
            print(f"  期望期号: {expected_period}")
            print(f"  实际期号: {current_target}")
            print(f"  期号正确: {'✅' if current_target == expected_period else '❌'}")
            
            # 模拟进度显示
            print(f"\n模拟进度显示:")
            print(f"已完成: 50/{test_periods} 期")
            print(f"当前数据库包含: {actual_db_size} 期数据")
            print(f"当前最新1期号码: {current_target} {' '.join(map(str, current_target_numbers[:6]))} + {current_target_numbers[6]}")
        
        # 检查数据可用性
        answer_data = system.data_loader.get_answer_data(current_target, 6)
        if answer_data is None:
            print(f"期号 {current_target} 后续数据不足，停止测试")
            break
    
    print(f"\n✅ 数据库范围修复测试完成")


def test_different_periods():
    """测试不同的数据库范围设置"""
    print("\n=== 测试不同的数据库范围设置 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 测试不同的数据库范围
    target_period = 20001
    test_periods_list = [100, 300, 500, 1000]
    
    for periods in test_periods_list:
        print(f"\n测试数据库范围: {periods} 期")
        
        # 更新数据库
        current_target_idx = system.raw_data[system.raw_data['NO'] == target_period].index[0]
        start_db_idx = max(0, current_target_idx + 1 - periods)
        system.current_db = system.raw_data.iloc[start_db_idx:current_target_idx + 1].copy()
        
        # 验证数据库大小
        expected_db_size = min(periods, current_target_idx + 1)
        actual_db_size = len(system.current_db)
        
        print(f"  期望数据库大小: {expected_db_size}")
        print(f"  实际数据库大小: {actual_db_size}")
        print(f"  数据库大小正确: {'✅' if actual_db_size == expected_db_size else '❌'}")
        
        # 验证数据库内容
        db_first_period = system.current_db.iloc[0]['NO']
        db_last_period = system.current_db.iloc[-1]['NO']
        print(f"  数据库期号范围: {int(db_first_period)} - {int(db_last_period)}")
        print(f"  数据库最后期号正确: {'✅' if int(db_last_period) == target_period else '❌'}")


def test_zero_periods():
    """测试periods=0的情况"""
    print("\n=== 测试periods=0的情况 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    target_period = 20001
    periods = 0  # 使用所有数据
    
    print(f"目标期号: {target_period}")
    print(f"数据库范围: 所有数据 (periods=0)")
    
    # 更新数据库
    current_target_idx = system.raw_data[system.raw_data['NO'] == target_period].index[0]
    system.current_db = system.raw_data.iloc[:current_target_idx + 1].copy()
    
    # 验证数据库大小
    expected_db_size = current_target_idx + 1
    actual_db_size = len(system.current_db)
    
    print(f"期望数据库大小: {expected_db_size}")
    print(f"实际数据库大小: {actual_db_size}")
    print(f"数据库大小正确: {'✅' if actual_db_size == expected_db_size else '❌'}")
    
    # 验证数据库内容
    db_first_period = system.current_db.iloc[0]['NO']
    db_last_period = system.current_db.iloc[-1]['NO']
    print(f"数据库期号范围: {int(db_first_period)} - {int(db_last_period)}")
    print(f"数据库最后期号正确: {'✅' if int(db_last_period) == target_period else '❌'}")


def main():
    """主测试函数"""
    print("数据库范围修复测试")
    print("=" * 50)
    
    # 测试数据库范围修复
    test_database_range_fix()
    
    # 测试不同的数据库范围设置
    test_different_periods()
    
    # 测试periods=0的情况
    test_zero_periods()
    
    print("\n" + "=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()
