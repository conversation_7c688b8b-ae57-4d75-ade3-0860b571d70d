"""
测试最终修复的功能
"""

from main import LotteryPredictionSystem


def test_year_transition():
    """测试年份跳转功能"""
    print("=== 测试年份跳转功能 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 获取所有可用期号列表
    available_periods = sorted([int(period) for period in system.raw_data['NO'].tolist()])
    print(f"数据库中共有 {len(available_periods)} 个期号")
    print(f"期号范围: {available_periods[0]} - {available_periods[-1]}")
    
    # 查找年份跳转的例子
    year_transitions = []
    for i in range(len(available_periods) - 1):
        current_period = available_periods[i]
        next_period = available_periods[i + 1]
        
        # 检查是否是年份跳转（例如从24152到25001）
        current_year = current_period // 1000
        next_year = next_period // 1000
        
        if next_year > current_year:
            year_transitions.append((current_period, next_period))
    
    print(f"找到 {len(year_transitions)} 个年份跳转:")
    for current, next_p in year_transitions[:5]:  # 只显示前5个
        print(f"  {current} -> {next_p}")
    
    if year_transitions:
        # 测试第一个年份跳转
        test_current, test_next = year_transitions[0]
        print(f"\n测试年份跳转: {test_current} -> {test_next}")
        
        # 找到测试期号在列表中的位置
        try:
            start_idx = available_periods.index(test_current)
            print(f"期号 {test_current} 在列表中的索引: {start_idx}")
            
            # 测试跳转逻辑
            for i in range(3):  # 测试3期
                if start_idx + i >= len(available_periods):
                    print("已到达数据末尾")
                    break
                    
                current_target = available_periods[start_idx + i]
                print(f"第{i+1}期: {current_target}")
                
                # 检查是否有足够的后续数据
                answer_data = system.data_loader.get_answer_data(current_target, 6)
                if answer_data is None:
                    print(f"  期号 {current_target} 后续数据不足")
                else:
                    print(f"  期号 {current_target} 有 {len(answer_data)} 期后续数据")
            
            print("✅ 年份跳转逻辑测试通过")
            
        except ValueError:
            print(f"❌ 期号 {test_current} 不在列表中")
    else:
        print("❌ 未找到年份跳转的例子")


def test_verbose_control():
    """测试打印信息控制功能"""
    print("\n=== 测试打印信息控制功能 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 选择一个测试期号
    available_periods = sorted([int(period) for period in system.raw_data['NO'].tolist()])
    test_period = available_periods[100]  # 选择第100个期号
    
    print(f"测试期号: {test_period}")
    
    # 更新当前数据库
    target_idx = system.raw_data[system.raw_data['NO'] == test_period].index[0]
    start_idx = max(0, target_idx + 1 - 500)
    system.current_db = system.raw_data.iloc[start_idx:target_idx + 1].copy()
    
    # 获取最新期信息
    latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
    
    # 计算必要的概率
    historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
    cold_probs = system.core_algo.calculate_cold_ball_probability(system.current_db)
    repeat_prob = system.core_algo.calculate_repeat_probability(system.current_db)
    follow_matrices = system.core_algo.calculate_following_probability(system.current_db)
    markov_probs = system.core_algo.calculate_markov_chain(
        latest_numbers, follow_matrices, historical_probs
    )
    
    print("\n测试 verbose=True (应该有打印信息):")
    red4, blue4 = system.prediction_algo.predict_group_4_markov_big(
        markov_probs, historical_probs, historical_probs, verbose=True
    )
    print(f"第4组预测结果: {sorted(red4)} + {blue4}")
    
    print("\n测试 verbose=False (应该没有打印信息):")
    red5, blue5 = system.prediction_algo.predict_group_5_markov_cold(
        markov_probs, historical_probs, cold_probs, system.current_db, verbose=False
    )
    print(f"第5组预测结果: {sorted(red5)} + {blue5}")
    
    print("\n✅ 打印信息控制功能测试通过")


def test_analysis_mode_simulation():
    """模拟分析模式运行"""
    print("\n=== 模拟分析模式运行 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 选择测试期号
    available_periods = sorted([int(period) for period in system.raw_data['NO'].tolist()])
    target_period = available_periods[50]  # 选择第50个期号
    
    print(f"测试期号: {target_period}")
    
    # 找到目标期号在列表中的位置
    try:
        start_idx = available_periods.index(target_period)
    except ValueError:
        print(f"目标期号 {target_period} 不存在")
        return
    
    # 模拟分析100期，测试每50期打印一次的逻辑
    test_periods = 100
    print_count = 0
    
    for i in range(test_periods):
        # 使用期号列表进行正确的跳转
        if start_idx + i >= len(available_periods):
            print("已到达数据末尾")
            break
            
        current_target = available_periods[start_idx + i]
        
        # 检查是否应该打印详细信息
        verbose_mode = (i + 1) % 50 == 0
        if verbose_mode:
            print_count += 1
            print(f"\n第{i+1}期 (期号{current_target}): 应该打印详细信息")
        
        # 检查数据可用性
        answer_data = system.data_loader.get_answer_data(current_target, 6)
        if answer_data is None:
            print(f"期号 {current_target} 后续数据不足，停止测试")
            break
    
    print(f"\n测试了 {min(test_periods, len(available_periods) - start_idx)} 期")
    print(f"应该打印详细信息的次数: {print_count}")
    print(f"预期打印次数: {test_periods // 50}")
    
    if print_count == test_periods // 50:
        print("✅ 每50期打印一次的逻辑正确")
    else:
        print("❌ 打印逻辑有问题")


def main():
    """主测试函数"""
    print("双色球预测程序最终修复测试")
    print("=" * 50)
    
    # 测试年份跳转功能
    test_year_transition()
    
    # 测试打印信息控制功能
    test_verbose_control()
    
    # 模拟分析模式运行
    test_analysis_mode_simulation()
    
    print("\n" + "=" * 50)
    print("最终修复测试完成！")


if __name__ == "__main__":
    main()
