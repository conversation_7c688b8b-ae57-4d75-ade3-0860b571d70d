"""
双色球彩票预测选号与分析比对程序
主程序入口
"""

import sys
import traceback
from datetime import datetime
from typing import List, Dict, Any

# 导入自定义模块
from data_loader import DataLoader
from core_algorithms import CoreAlgorithms
from prediction_algorithms import PredictionAlgorithms
from advanced_predictions import AdvancedPredictions
from comparison_algorithms import ComparisonAlgorithms
from user_interface import UserInterface


class LotteryPredictionSystem:
    """双色球预测系统主类"""
    
    def __init__(self):
        """初始化系统"""
        self.data_loader = DataLoader()
        self.core_algo = CoreAlgorithms()
        self.prediction_algo = PredictionAlgorithms()
        self.advanced_pred = AdvancedPredictions()
        self.comparison_algo = ComparisonAlgorithms()
        self.ui = UserInterface()
        
        # 数据存储
        self.raw_data = None
        self.current_db = None
        
    def initialize_data(self):
        """初始化数据"""
        try:
            print("正在加载数据...")
            self.raw_data = self.data_loader.load_data()
            
            if not self.data_loader.validate_data(self.raw_data):
                raise ValueError("数据验证失败")
                
            print("数据加载完成")
            return True
            
        except Exception as e:
            print(f"数据初始化失败: {e}")
            return False
    
    def run_prediction_mode(self):
        """运行预测选号模式"""
        try:
            # 获取数据库范围
            periods = self.ui.get_database_range("prediction")
            self.current_db = self.data_loader.get_current_database(periods)
            
            # 获取最新期信息
            latest_period, latest_numbers, _ = self.data_loader.get_latest_period_info(self.current_db)
            
            # 打印头部信息
            self.ui.print_prediction_header(len(self.current_db), latest_period, latest_numbers)
            
            # 计算各种概率
            print("\n正在计算历史概率...")
            historical_probs = self.core_algo.calculate_historical_probability(self.current_db)
            
            print("正在计算冷球概率...")
            cold_probs = self.core_algo.calculate_cold_ball_probability(self.current_db)
            
            print("正在计算重号概率...")
            repeat_prob = self.core_algo.calculate_repeat_probability(self.current_db)
            
            print("正在计算跟随性概率...")
            follow_matrices = self.core_algo.calculate_following_probability(self.current_db)
            
            print("正在计算马尔科夫链概率...")
            markov_probs = self.core_algo.calculate_markov_chain(
                latest_numbers, follow_matrices, historical_probs
            )
            
            print("正在计算贝叶斯概率...")
            bayesian_probs = self.core_algo.calculate_bayesian_probability(
                latest_numbers, follow_matrices, historical_probs
            )
            
            # 打印要求信息
            red_big_req = historical_probs['red_big_prob'].loc[
                historical_probs['red_big_prob']['probability'].idxmax(), 'big_count'
            ]
            blue_big_req = historical_probs['blue_big_prob'].loc[
                historical_probs['blue_big_prob']['probability'].idxmax(), 'big_count'
            ]
            red_cold_req = cold_probs['red_cold_prob'].loc[
                cold_probs['red_cold_prob']['probability'].idxmax(), 'cold_count'
            ]
            blue_cold_req = cold_probs['blue_cold_prob'].loc[
                cold_probs['blue_cold_prob']['probability'].idxmax(), 'cold_count'
            ]
            red_repeat_req = repeat_prob.loc[
                repeat_prob['probability'].idxmax(), 'repeat_count'
            ]
            
            self.ui.print_prediction_requirements(
                red_big_req, blue_big_req, red_cold_req, blue_cold_req, red_repeat_req
            )
            
            # 获取冷球信息
            red_cold_balls = self.prediction_algo.get_cold_balls(self.current_db, 'red')
            blue_cold_balls = self.prediction_algo.get_cold_balls(self.current_db, 'blue')
            self.ui.print_cold_balls_info(red_cold_balls, blue_cold_balls)
            
            # 进行预测
            print("\n=== 预测结果 ===")
            predictions = []
            
            # 第1组：马尔科夫链
            red1, blue1 = self.prediction_algo.predict_group_1_markov(markov_probs)
            self.ui.print_prediction_result(1, red1, blue1)
            predictions.append((red1, blue1))
            
            # 第2组：贝叶斯概率
            red2, blue2 = self.prediction_algo.predict_group_2_bayesian(bayesian_probs)
            self.ui.print_prediction_result(2, red2, blue2)
            predictions.append((red2, blue2))
            
            # 第3组：历史出现概率
            red3, blue3 = self.prediction_algo.predict_group_3_historical(historical_probs)
            self.ui.print_prediction_result(3, red3, blue3)
            predictions.append((red3, blue3))
            
            # 第4组：马尔科夫链+大球筛选
            red4, blue4 = self.prediction_algo.predict_group_4_markov_big(
                markov_probs, historical_probs, historical_probs
            )
            self.ui.print_prediction_result(4, red4, blue4)
            predictions.append((red4, blue4))
            
            # 第5组：马尔科夫链+冷球筛选
            red5, blue5 = self.prediction_algo.predict_group_5_markov_cold(
                markov_probs, historical_probs, cold_probs, self.current_db
            )
            self.ui.print_prediction_result(5, red5, blue5)
            predictions.append((red5, blue5))
            
            # 第6组：马尔科夫链+重号筛选
            red6, blue6 = self.prediction_algo.predict_group_6_markov_repeat(
                markov_probs, historical_probs, repeat_prob, latest_numbers
            )
            self.ui.print_prediction_result(6, red6, blue6)
            predictions.append((red6, blue6))
            
            # 第7组：马尔科夫链+大球冷球筛选
            red7, blue7 = self.advanced_pred.predict_group_7_markov_big_cold(
                markov_probs, historical_probs, historical_probs, cold_probs, self.current_db
            )
            self.ui.print_prediction_result(7, red7, blue7)
            predictions.append((red7, blue7))
            
            # 第8组：马尔科夫链+大球重号筛选
            red8, blue8 = self.advanced_pred.predict_group_8_markov_big_repeat(
                markov_probs, historical_probs, historical_probs, repeat_prob, latest_numbers
            )
            self.ui.print_prediction_result(8, red8, blue8)
            predictions.append((red8, blue8))

            # 第9组：马尔科夫链+冷球重号筛选
            red9, blue9 = self.advanced_pred.predict_group_9_markov_cold_repeat(
                markov_probs, historical_probs, cold_probs, repeat_prob, self.current_db, latest_numbers
            )
            self.ui.print_prediction_result(9, red9, blue9)
            predictions.append((red9, blue9))

            # 第10组：马尔科夫链+大球冷球重号筛选
            red10, blue10 = self.advanced_pred.predict_group_10_markov_big_cold_repeat(
                markov_probs, historical_probs, historical_probs, cold_probs, repeat_prob,
                self.current_db, latest_numbers
            )
            self.ui.print_prediction_result(10, red10, blue10)
            predictions.append((red10, blue10))
            
            # 询问是否保存统计表格
            if self.ui.ask_save_tables():
                all_tables = {
                    'historical_probs': historical_probs,
                    'cold_probs': cold_probs,
                    'repeat_prob': repeat_prob,
                    'follow_matrices': follow_matrices,
                    'markov_probs': markov_probs,
                    'bayesian_probs': bayesian_probs,
                    'predictions': predictions
                }
                
                self.ui.save_prediction_tables(all_tables, len(self.current_db))
            
            print("\n预测选号完成！")
            
        except Exception as e:
            print(f"预测模式运行失败: {e}")
            traceback.print_exc()
    
    def run_analysis_mode(self):
        """运行分析比对模式"""
        try:
            # 获取可用期号列表
            available_periods = [str(int(period)) for period in self.raw_data['NO'].tolist()]
            
            # 获取目标期号
            target_period = self.ui.get_target_period(available_periods)
            target_period_int = int(target_period)
            
            # 获取数据库范围
            periods = self.ui.get_database_range("analysis")
            
            # 计算总分析期数
            target_idx = self.raw_data[self.raw_data['NO'] == target_period_int].index[0]
            max_possible_periods = len(self.raw_data) - target_idx - 6  # 需要6期答案数据
            
            if max_possible_periods <= 0:
                print("数据不足，无法进行分析比对")
                return
            
            # 打印头部信息
            if periods == 0:
                current_db_size = target_idx + 1
            else:
                current_db_size = min(periods, target_idx + 1)
            
            self.ui.print_analysis_header(max_possible_periods, target_period, current_db_size)
            
            # 开始分析比对
            all_results = []

            # 获取所有可用期号列表，用于正确的期号跳转
            available_periods = sorted([int(period) for period in self.raw_data['NO'].tolist()])

            # 找到目标期号在列表中的位置
            try:
                start_idx = available_periods.index(target_period_int)
            except ValueError:
                print(f"目标期号 {target_period_int} 不存在")
                return

            for i in range(max_possible_periods):
                # 使用期号列表进行正确的跳转，而不是简单的+1
                if start_idx + i >= len(available_periods):
                    print("已到达数据末尾")
                    break

                current_target = available_periods[start_idx + i]
                
                # 更新当前数据库
                if periods == 0:
                    # 使用目标期号及之前的所有数据
                    current_target_idx = self.raw_data[self.raw_data['NO'] == current_target].index[0]
                    self.current_db = self.raw_data.iloc[:current_target_idx + 1].copy()
                else:
                    # 使用目标期号及之前的N期数据
                    current_target_idx = self.raw_data[self.raw_data['NO'] == current_target].index[0]
                    start_idx = max(0, current_target_idx + 1 - periods)
                    self.current_db = self.raw_data.iloc[start_idx:current_target_idx + 1].copy()
                
                # 获取答案数据
                answer_data = self.data_loader.get_answer_data(current_target, 6)
                if answer_data is None:
                    break
                
                # 进行预测（完整版，包含所有10组）
                try:
                    # 获取最新期信息
                    latest_period, latest_numbers, _ = self.data_loader.get_latest_period_info(self.current_db)

                    # 计算必要的概率
                    historical_probs = self.core_algo.calculate_historical_probability(self.current_db)
                    cold_probs = self.core_algo.calculate_cold_ball_probability(self.current_db)
                    repeat_prob = self.core_algo.calculate_repeat_probability(self.current_db)
                    follow_matrices = self.core_algo.calculate_following_probability(self.current_db)
                    markov_probs = self.core_algo.calculate_markov_chain(
                        latest_numbers, follow_matrices, historical_probs
                    )
                    bayesian_probs = self.core_algo.calculate_bayesian_probability(
                        latest_numbers, follow_matrices, historical_probs
                    )

                    # 预测所有10组
                    predictions = []

                    # 第1组：马尔科夫链
                    red1, blue1 = self.prediction_algo.predict_group_1_markov(markov_probs)
                    predictions.append(red1 + [blue1])

                    # 第2组：贝叶斯概率
                    red2, blue2 = self.prediction_algo.predict_group_2_bayesian(bayesian_probs)
                    predictions.append(red2 + [blue2])

                    # 第3组：历史出现概率
                    red3, blue3 = self.prediction_algo.predict_group_3_historical(historical_probs)
                    predictions.append(red3 + [blue3])

                    # 第4组：马尔科夫链+大球筛选
                    # 只在每50期打印一次详细信息
                    verbose_mode = (i + 1) % 50 == 0
                    red4, blue4 = self.prediction_algo.predict_group_4_markov_big(
                        markov_probs, historical_probs, historical_probs, verbose=verbose_mode
                    )
                    predictions.append(red4 + [blue4])

                    # 第5组：马尔科夫链+冷球筛选
                    red5, blue5 = self.prediction_algo.predict_group_5_markov_cold(
                        markov_probs, historical_probs, cold_probs, self.current_db, verbose=verbose_mode
                    )
                    predictions.append(red5 + [blue5])

                    # 第6组：马尔科夫链+重号筛选
                    red6, blue6 = self.prediction_algo.predict_group_6_markov_repeat(
                        markov_probs, historical_probs, repeat_prob, latest_numbers, verbose=verbose_mode
                    )
                    predictions.append(red6 + [blue6])

                    # 第7组：马尔科夫链+大球冷球筛选
                    red7, blue7 = self.advanced_pred.predict_group_7_markov_big_cold(
                        markov_probs, historical_probs, historical_probs, cold_probs, self.current_db, verbose=verbose_mode
                    )
                    predictions.append(red7 + [blue7])

                    # 第8组：马尔科夫链+大球重号筛选
                    red8, blue8 = self.advanced_pred.predict_group_8_markov_big_repeat(
                        markov_probs, historical_probs, historical_probs, repeat_prob, latest_numbers
                    )
                    predictions.append(red8 + [blue8])

                    # 第9组：马尔科夫链+冷球重号筛选
                    red9, blue9 = self.advanced_pred.predict_group_9_markov_cold_repeat(
                        markov_probs, historical_probs, cold_probs, repeat_prob, self.current_db, latest_numbers
                    )
                    predictions.append(red9 + [blue9])

                    # 第10组：马尔科夫链+大球冷球重号筛选
                    red10, blue10 = self.advanced_pred.predict_group_10_markov_big_cold_repeat(
                        markov_probs, historical_probs, historical_probs, cold_probs, repeat_prob,
                        self.current_db, latest_numbers
                    )
                    predictions.append(red10 + [blue10])
                    
                    # 准备答案数据
                    answers = []
                    for _, row in answer_data.iterrows():
                        answer = [row['r1'], row['r2'], row['r3'], row['r4'], row['r5'], row['r6'], row['b']]
                        answers.append(answer)
                    
                    # 比对
                    comparison_result = self.comparison_algo.compare_predictions_with_answers(predictions, answers)
                    
                    # 格式化结果
                    formatted_result = self.comparison_algo.format_comparison_result(
                        str(current_target), predictions, answers, comparison_result
                    )
                    
                    all_results.append(formatted_result)
                    
                    # 每50期打印一次进度
                    if (i + 1) % 50 == 0:
                        # 计算当前期的统计信息
                        latest_stats = {
                            'red_big': self.core_algo.calculate_big_ball_count(latest_numbers[:6], 'red'),
                            'blue_big': self.core_algo.calculate_big_ball_count([latest_numbers[6]], 'blue'),
                            'red_cold': 0,  # 简化处理
                            'blue_cold': 0,  # 简化处理
                            'red_repeat': 0  # 简化处理
                        }
                        
                        self.comparison_algo.print_progress_info(
                            i + 1, max_possible_periods, len(self.current_db),
                            latest_period, latest_numbers, latest_stats
                        )
                
                except Exception as e:
                    print(f"分析期号 {current_target} 时出错: {e}")
                    continue
            
            # 保存结果
            if all_results:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"分析比对结果_{target_period}_{timestamp}.xlsx"
                self.comparison_algo.save_comparison_results(all_results, output_file)
                
                # 打印最终统计
                distribution = self.comparison_algo.analyze_hit_distribution(all_results)
                print("\n=== 最终命中分布统计 ===")
                for group_name, group_dist in distribution.items():
                    total = sum(group_dist.values())
                    if total > 0:
                        avg_hits = sum(hits * count for hits, count in group_dist.items()) / total
                        print(f"{group_name}: 平均命中 {avg_hits:.2f} 球")
            
            print("\n分析比对完成！")
            
        except Exception as e:
            print(f"分析模式运行失败: {e}")
            traceback.print_exc()
    
    def run(self):
        """运行主程序"""
        try:
            # 初始化数据
            if not self.initialize_data():
                return
            
            # 获取用户选择
            choice = self.ui.get_main_function_choice()
            
            if choice == 1:
                self.run_prediction_mode()
            elif choice == 2:
                self.run_analysis_mode()
            
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行失败: {e}")
            traceback.print_exc()


def main():
    """主函数"""
    system = LotteryPredictionSystem()
    system.run()


if __name__ == "__main__":
    main()
