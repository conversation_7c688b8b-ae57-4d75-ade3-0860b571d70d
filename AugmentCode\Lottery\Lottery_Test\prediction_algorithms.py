"""
预测算法模块
实现10组不同的预测算法
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Any
from core_algorithms import CoreAlgorithms


class PredictionAlgorithms:
    """预测算法类"""
    
    def __init__(self):
        """初始化预测算法"""
        self.core_algo = CoreAlgorithms()
    
    def format_numbers(self, red_balls: List[int], blue_ball: int) -> str:
        """
        格式化号码显示
        
        Args:
            red_balls: 红球号码列表
            blue_ball: 蓝球号码
            
        Returns:
            格式化的号码字符串
        """
        red_sorted = sorted(red_balls)
        return f"{' '.join(map(str, red_sorted))} + {blue_ball}"
    
    def get_cold_balls(self, current_db: pd.DataFrame, ball_type: str) -> List[int]:
        """
        获取冷球号码列表
        
        Args:
            current_db: 当前数据库
            ball_type: 球类型 'red' 或 'blue'
            
        Returns:
            冷球号码列表
        """
        if len(current_db) < 6:
            return []
        
        # 获取最新一期和前5期数据
        latest_row = current_db.iloc[-1]
        previous_5_rows = current_db.iloc[-6:-1]
        
        if ball_type == 'red':
            # 前5期所有红球号码
            previous_numbers = set()
            for _, row in previous_5_rows.iterrows():
                red_numbers = [row['r1'], row['r2'], row['r3'], row['r4'], row['r5'], row['r6']]
                previous_numbers.update(red_numbers)
            
            # 找出1-33中未在前5期出现的号码
            all_red_numbers = set(range(1, 34))
            cold_balls = list(all_red_numbers - previous_numbers)
            
        else:  # blue
            # 前5期所有蓝球号码
            previous_blues = set([row['b'] for _, row in previous_5_rows.iterrows()])
            
            # 找出1-16中未在前5期出现的号码
            all_blue_numbers = set(range(1, 17))
            cold_balls = list(all_blue_numbers - previous_blues)
        
        return sorted(cold_balls)
    
    def predict_group_1_markov(self, markov_probs: Dict[str, np.ndarray]) -> Tuple[List[int], int]:
        """
        第1组预测：马尔科夫链
        
        Args:
            markov_probs: 马尔科夫链概率
            
        Returns:
            (红球列表, 蓝球)
        """
        red_markov = markov_probs['red_markov']
        blue_markov = markov_probs['blue_markov']
        
        # 选择概率最大的6个红球
        red_indices = np.argsort(red_markov)[-6:]
        red_balls = [i + 1 for i in red_indices]
        
        # 选择概率最大的1个蓝球
        blue_index = np.argmax(blue_markov)
        blue_ball = blue_index + 1
        
        return red_balls, blue_ball
    
    def predict_group_2_bayesian(self, bayesian_probs: Dict[str, np.ndarray]) -> Tuple[List[int], int]:
        """
        第2组预测：贝叶斯概率
        
        Args:
            bayesian_probs: 贝叶斯概率
            
        Returns:
            (红球列表, 蓝球)
        """
        red_bayesian = bayesian_probs['red_bayesian']
        blue_bayesian = bayesian_probs['blue_bayesian']
        
        # 选择概率最大的6个红球
        red_indices = np.argsort(red_bayesian)[-6:]
        red_balls = [i + 1 for i in red_indices]
        
        # 选择概率最大的1个蓝球
        blue_index = np.argmax(blue_bayesian)
        blue_ball = blue_index + 1
        
        return red_balls, blue_ball
    
    def predict_group_3_historical(self, historical_probs: Dict[str, pd.DataFrame]) -> Tuple[List[int], int]:
        """
        第3组预测：历史出现概率
        
        Args:
            historical_probs: 历史出现概率
            
        Returns:
            (红球列表, 蓝球)
        """
        red_prob = historical_probs['red_number_prob']
        blue_prob = historical_probs['blue_number_prob']
        
        # 选择概率最大的6个红球
        red_sorted = red_prob.sort_values('probability', ascending=False)
        red_balls = red_sorted.head(6)['number'].tolist()
        
        # 选择概率最大的1个蓝球
        blue_sorted = blue_prob.sort_values('probability', ascending=False)
        blue_ball = blue_sorted.iloc[0]['number']
        
        return red_balls, blue_ball
    
    def predict_group_4_markov_big(self, markov_probs: Dict[str, np.ndarray],
                                  historical_probs: Dict[str, pd.DataFrame],
                                  big_probs: Dict[str, pd.DataFrame]) -> Tuple[List[int], int]:
        """
        第4组预测：马尔科夫链+大球历史出现概率筛选
        
        Args:
            markov_probs: 马尔科夫链概率
            historical_probs: 历史出现概率
            big_probs: 大球概率
            
        Returns:
            (红球列表, 蓝球)
        """
        # 获取要求的大球数
        red_big_prob = big_probs['red_big_prob']
        blue_big_prob = big_probs['blue_big_prob']
        
        required_red_big = red_big_prob.loc[red_big_prob['probability'].idxmax(), 'big_count']
        required_blue_big = blue_big_prob.loc[blue_big_prob['probability'].idxmax(), 'big_count']
        
        print(f"基于历史出现概率要求的红蓝球大球数：{required_red_big}、{required_blue_big}")
        
        # 从马尔科夫链预测开始
        initial_red, initial_blue = self.predict_group_1_markov(markov_probs)
        
        # 调整红球大球数
        red_balls = self._adjust_red_balls_for_big_count(
            initial_red, required_red_big, historical_probs['red_number_prob']
        )
        
        # 调整蓝球大球数
        blue_ball = self._adjust_blue_ball_for_big_count(
            initial_blue, required_blue_big, historical_probs['blue_number_prob']
        )
        
        return red_balls, blue_ball
    
    def _adjust_red_balls_for_big_count(self, red_balls: List[int], required_big: int, 
                                       red_prob: pd.DataFrame) -> List[int]:
        """
        调整红球以满足大球数要求
        
        Args:
            red_balls: 初始红球列表
            required_big: 要求的大球数
            red_prob: 红球概率表
            
        Returns:
            调整后的红球列表
        """
        current_big = self.core_algo.calculate_big_ball_count(red_balls, 'red')
        
        if current_big == required_big:
            return red_balls
        
        # 创建概率字典便于查找
        prob_dict = dict(zip(red_prob['number'], red_prob['probability']))
        
        result_balls = red_balls.copy()
        
        if current_big > required_big:
            # 需要减少大球
            big_balls_in_result = [ball for ball in result_balls if ball > 16]
            # 按概率从小到大排序，优先移除概率小的
            big_balls_sorted = sorted(big_balls_in_result, key=lambda x: prob_dict[x])
            
            # 移除多余的大球
            to_remove = current_big - required_big
            for i in range(to_remove):
                if i < len(big_balls_sorted):
                    result_balls.remove(big_balls_sorted[i])
            
            # 补充非大球
            all_numbers = set(range(1, 34))
            excluded = set(result_balls)
            small_balls = [ball for ball in all_numbers - excluded if ball <= 16]
            small_balls_sorted = sorted(small_balls, key=lambda x: prob_dict[x], reverse=True)
            
            # 补充到6个
            for ball in small_balls_sorted:
                if len(result_balls) < 6:
                    result_balls.append(ball)
                else:
                    break
        
        else:
            # 需要增加大球
            all_numbers = set(range(1, 34))
            excluded = set(result_balls)
            big_balls = [ball for ball in all_numbers - excluded if ball > 16]
            big_balls_sorted = sorted(big_balls, key=lambda x: prob_dict[x], reverse=True)
            
            # 添加大球
            to_add = required_big - current_big
            for i in range(min(to_add, len(big_balls_sorted))):
                result_balls.append(big_balls_sorted[i])
            
            # 如果添加后超过6个，移除概率最小的非大球
            if len(result_balls) > 6:
                small_balls_in_result = [ball for ball in result_balls if ball <= 16]
                small_balls_sorted = sorted(small_balls_in_result, key=lambda x: prob_dict[x])
                
                to_remove = len(result_balls) - 6
                for i in range(to_remove):
                    if i < len(small_balls_sorted):
                        result_balls.remove(small_balls_sorted[i])
        
        return result_balls[:6]  # 确保只有6个球
    
    def _adjust_blue_ball_for_big_count(self, blue_ball: int, required_big: int,
                                       blue_prob: pd.DataFrame) -> int:
        """
        调整蓝球以满足大球数要求
        
        Args:
            blue_ball: 初始蓝球
            required_big: 要求的大球数
            blue_prob: 蓝球概率表
            
        Returns:
            调整后的蓝球
        """
        current_big = self.core_algo.calculate_big_ball_count([blue_ball], 'blue')
        
        if current_big == required_big:
            return blue_ball
        
        # 创建概率字典
        prob_dict = dict(zip(blue_prob['number'], blue_prob['probability']))
        
        if required_big == 1:
            # 需要大球 (>8)
            big_balls = [ball for ball in range(9, 17)]
            big_balls_sorted = sorted(big_balls, key=lambda x: prob_dict[x], reverse=True)
            return big_balls_sorted[0] if big_balls_sorted else blue_ball
        else:
            # 需要小球 (<=8)
            small_balls = [ball for ball in range(1, 9)]
            small_balls_sorted = sorted(small_balls, key=lambda x: prob_dict[x], reverse=True)
            return small_balls_sorted[0] if small_balls_sorted else blue_ball

    def predict_group_5_markov_cold(self, markov_probs: Dict[str, np.ndarray],
                                   historical_probs: Dict[str, pd.DataFrame],
                                   cold_probs: Dict[str, pd.DataFrame],
                                   current_db: pd.DataFrame) -> Tuple[List[int], int]:
        """
        第5组预测：马尔科夫链+冷球历史出现概率筛选

        Args:
            markov_probs: 马尔科夫链概率
            historical_probs: 历史出现概率
            cold_probs: 冷球概率
            current_db: 当前数据库

        Returns:
            (红球列表, 蓝球)
        """
        # 获取要求的冷球数
        red_cold_prob = cold_probs['red_cold_prob']
        blue_cold_prob = cold_probs['blue_cold_prob']

        required_red_cold = red_cold_prob.loc[red_cold_prob['probability'].idxmax(), 'cold_count']
        required_blue_cold = blue_cold_prob.loc[blue_cold_prob['probability'].idxmax(), 'cold_count']

        print(f"基于历史出现概率要求的红蓝球冷球数：{required_red_cold}、{required_blue_cold}")

        # 获取冷球号码
        red_cold_balls = self.get_cold_balls(current_db, 'red')
        blue_cold_balls = self.get_cold_balls(current_db, 'blue')

        print(f"红球冷球号码：{red_cold_balls}")
        print(f"蓝球冷球号码：{blue_cold_balls}")

        # 从马尔科夫链预测开始
        initial_red, initial_blue = self.predict_group_1_markov(markov_probs)

        # 调整红球冷球数
        red_balls = self._adjust_red_balls_for_cold_count(
            initial_red, required_red_cold, red_cold_balls, historical_probs['red_number_prob']
        )

        # 调整蓝球冷球数
        blue_ball = self._adjust_blue_ball_for_cold_count(
            initial_blue, required_blue_cold, blue_cold_balls, historical_probs['blue_number_prob']
        )

        return red_balls, blue_ball

    def _adjust_red_balls_for_cold_count(self, red_balls: List[int], required_cold: int,
                                        cold_balls: List[int], red_prob: pd.DataFrame) -> List[int]:
        """
        调整红球以满足冷球数要求

        Args:
            red_balls: 初始红球列表
            required_cold: 要求的冷球数
            cold_balls: 冷球号码列表
            red_prob: 红球概率表

        Returns:
            调整后的红球列表
        """
        current_cold = len([ball for ball in red_balls if ball in cold_balls])

        if current_cold == required_cold:
            return red_balls

        prob_dict = dict(zip(red_prob['number'], red_prob['probability']))
        result_balls = red_balls.copy()

        if current_cold > required_cold:
            # 需要减少冷球
            cold_balls_in_result = [ball for ball in result_balls if ball in cold_balls]
            cold_balls_sorted = sorted(cold_balls_in_result, key=lambda x: prob_dict[x])

            to_remove = current_cold - required_cold
            for i in range(to_remove):
                if i < len(cold_balls_sorted):
                    result_balls.remove(cold_balls_sorted[i])

            # 补充非冷球
            all_numbers = set(range(1, 34))
            excluded = set(result_balls)
            non_cold_balls = [ball for ball in all_numbers - excluded if ball not in cold_balls]
            non_cold_sorted = sorted(non_cold_balls, key=lambda x: prob_dict[x], reverse=True)

            for ball in non_cold_sorted:
                if len(result_balls) < 6:
                    result_balls.append(ball)
                else:
                    break

        else:
            # 需要增加冷球
            all_numbers = set(range(1, 34))
            excluded = set(result_balls)
            available_cold = [ball for ball in cold_balls if ball not in excluded]
            available_cold_sorted = sorted(available_cold, key=lambda x: prob_dict[x], reverse=True)

            to_add = required_cold - current_cold
            for i in range(min(to_add, len(available_cold_sorted))):
                result_balls.append(available_cold_sorted[i])

            # 如果添加后超过6个，移除概率最小的非冷球
            if len(result_balls) > 6:
                non_cold_in_result = [ball for ball in result_balls if ball not in cold_balls]
                non_cold_sorted = sorted(non_cold_in_result, key=lambda x: prob_dict[x])

                to_remove = len(result_balls) - 6
                for i in range(to_remove):
                    if i < len(non_cold_sorted):
                        result_balls.remove(non_cold_sorted[i])

        return result_balls[:6]

    def _adjust_blue_ball_for_cold_count(self, blue_ball: int, required_cold: int,
                                        cold_balls: List[int], blue_prob: pd.DataFrame) -> int:
        """
        调整蓝球以满足冷球数要求

        Args:
            blue_ball: 初始蓝球
            required_cold: 要求的冷球数
            cold_balls: 冷球号码列表
            blue_prob: 蓝球概率表

        Returns:
            调整后的蓝球
        """
        current_cold = 1 if blue_ball in cold_balls else 0

        if current_cold == required_cold:
            return blue_ball

        prob_dict = dict(zip(blue_prob['number'], blue_prob['probability']))

        if required_cold == 1:
            # 需要冷球
            if cold_balls:
                cold_balls_sorted = sorted(cold_balls, key=lambda x: prob_dict[x], reverse=True)
                return cold_balls_sorted[0]
        else:
            # 需要非冷球
            non_cold_balls = [ball for ball in range(1, 17) if ball not in cold_balls]
            if non_cold_balls:
                non_cold_sorted = sorted(non_cold_balls, key=lambda x: prob_dict[x], reverse=True)
                return non_cold_sorted[0]

        return blue_ball

    def predict_group_6_markov_repeat(self, markov_probs: Dict[str, np.ndarray],
                                     historical_probs: Dict[str, pd.DataFrame],
                                     repeat_prob: pd.DataFrame,
                                     latest_numbers: List[int]) -> Tuple[List[int], int]:
        """
        第6组预测：马尔科夫链+红球重号历史出现概率筛选

        Args:
            markov_probs: 马尔科夫链概率
            historical_probs: 历史出现概率
            repeat_prob: 重号概率表
            latest_numbers: 最新一期号码

        Returns:
            (红球列表, 蓝球)
        """
        # 获取要求的重号数
        required_repeat = repeat_prob.loc[repeat_prob['probability'].idxmax(), 'repeat_count']
        print(f"基于历史出现概率要求的红球重号数：{required_repeat}")

        # 从马尔科夫链预测开始
        initial_red, initial_blue = self.predict_group_1_markov(markov_probs)

        # 调整红球重号数
        red_balls = self._adjust_red_balls_for_repeat_count(
            initial_red, required_repeat, latest_numbers[:6], historical_probs['red_number_prob']
        )

        return red_balls, initial_blue

    def _adjust_red_balls_for_repeat_count(self, red_balls: List[int], required_repeat: int,
                                          latest_red: List[int], red_prob: pd.DataFrame) -> List[int]:
        """
        调整红球以满足重号数要求

        Args:
            red_balls: 初始红球列表
            required_repeat: 要求的重号数
            latest_red: 最新一期红球号码
            red_prob: 红球概率表

        Returns:
            调整后的红球列表
        """
        current_repeat = len(set(red_balls).intersection(set(latest_red)))

        if current_repeat == required_repeat:
            return red_balls

        prob_dict = dict(zip(red_prob['number'], red_prob['probability']))
        result_balls = red_balls.copy()

        if current_repeat > required_repeat:
            # 需要减少重号
            repeat_balls_in_result = [ball for ball in result_balls if ball in latest_red]
            repeat_balls_sorted = sorted(repeat_balls_in_result, key=lambda x: prob_dict[x])

            to_remove = current_repeat - required_repeat
            for i in range(to_remove):
                if i < len(repeat_balls_sorted):
                    result_balls.remove(repeat_balls_sorted[i])

            # 补充非重号
            all_numbers = set(range(1, 34))
            excluded = set(result_balls)
            non_repeat_balls = [ball for ball in all_numbers - excluded if ball not in latest_red]
            non_repeat_sorted = sorted(non_repeat_balls, key=lambda x: prob_dict[x], reverse=True)

            for ball in non_repeat_sorted:
                if len(result_balls) < 6:
                    result_balls.append(ball)
                else:
                    break

        else:
            # 需要增加重号
            all_numbers = set(range(1, 34))
            excluded = set(result_balls)
            available_repeat = [ball for ball in latest_red if ball not in excluded]
            available_repeat_sorted = sorted(available_repeat, key=lambda x: prob_dict[x], reverse=True)

            to_add = required_repeat - current_repeat
            for i in range(min(to_add, len(available_repeat_sorted))):
                result_balls.append(available_repeat_sorted[i])

            # 如果添加后超过6个，移除概率最小的非重号
            if len(result_balls) > 6:
                non_repeat_in_result = [ball for ball in result_balls if ball not in latest_red]
                non_repeat_sorted = sorted(non_repeat_in_result, key=lambda x: prob_dict[x])

                to_remove = len(result_balls) - 6
                for i in range(to_remove):
                    if i < len(non_repeat_sorted):
                        result_balls.remove(non_repeat_sorted[i])

        return result_balls[:6]
