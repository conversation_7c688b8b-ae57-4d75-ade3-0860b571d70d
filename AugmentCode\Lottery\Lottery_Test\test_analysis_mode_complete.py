"""
测试完整的分析模式功能
"""

from main import LotteryPredictionSystem


def test_analysis_mode_complete():
    """测试完整的分析模式功能"""
    print("=== 测试完整的分析模式功能 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 模拟用户输入
    target_period = "20001"  # 用户输入的目标期号
    periods = 571  # 用户输入的数据库范围
    
    print(f"目标期号: {target_period}")
    print(f"数据库范围: {periods} 期")
    
    # 获取可用期号列表
    available_periods = sorted([int(period) for period in system.raw_data['NO'].tolist()])
    target_period_int = int(target_period)
    
    # 检查目标期号是否存在
    if target_period_int not in available_periods:
        print(f"目标期号 {target_period_int} 不存在")
        return
    
    # 计算总分析期数
    target_idx = system.raw_data[system.raw_data['NO'] == target_period_int].index[0]
    max_possible_periods = len(system.raw_data) - target_idx - 6  # 需要6期答案数据
    
    if max_possible_periods <= 0:
        print("数据不足，无法进行分析比对")
        return
    
    # 限制测试期数
    test_periods = min(55, max_possible_periods)  # 测试55期，这样会有一次50期的打印
    
    print(f"目标期号索引: {target_idx}")
    print(f"最大可分析期数: {max_possible_periods}")
    print(f"将测试 {test_periods} 期")
    
    # 开始分析比对
    all_results = []
    
    # 找到目标期号在列表中的位置
    try:
        start_idx = available_periods.index(target_period_int)
    except ValueError:
        print(f"目标期号 {target_period_int} 不存在")
        return
    
    for i in range(test_periods):
        # 使用期号列表进行正确的跳转，而不是简单的+1
        if start_idx + i >= len(available_periods):
            print("已到达数据末尾")
            break
            
        current_target = available_periods[start_idx + i]
        
        # 更新当前数据库
        if periods == 0:
            # 使用目标期号及之前的所有数据
            current_target_idx = system.raw_data[system.raw_data['NO'] == current_target].index[0]
            system.current_db = system.raw_data.iloc[:current_target_idx + 1].copy()
        else:
            # 使用目标期号及之前的N期数据
            current_target_idx = system.raw_data[system.raw_data['NO'] == current_target].index[0]
            start_db_idx = max(0, current_target_idx + 1 - periods)
            system.current_db = system.raw_data.iloc[start_db_idx:current_target_idx + 1].copy()
        
        # 获取答案数据
        answer_data = system.data_loader.get_answer_data(current_target, 6)
        if answer_data is None:
            print(f"期号 {current_target} 后续数据不足，跳过")
            break
        
        # 进行预测（完整版，包含所有10组）
        try:
            # 获取最新期信息
            latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
            
            # 计算必要的概率
            historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
            cold_probs = system.core_algo.calculate_cold_ball_probability(system.current_db)
            repeat_prob = system.core_algo.calculate_repeat_probability(system.current_db)
            follow_matrices = system.core_algo.calculate_following_probability(system.current_db)
            markov_probs = system.core_algo.calculate_markov_chain(
                latest_numbers, follow_matrices, historical_probs
            )
            bayesian_probs = system.core_algo.calculate_bayesian_probability(
                latest_numbers, follow_matrices, historical_probs
            )
            
            # 预测所有10组
            predictions = []
            
            # 只在每50期打印一次详细信息
            verbose_mode = (i + 1) % 50 == 0
            
            # 第1组：马尔科夫链
            red1, blue1 = system.prediction_algo.predict_group_1_markov(markov_probs)
            predictions.append(red1 + [blue1])
            
            # 第2组：贝叶斯概率
            red2, blue2 = system.prediction_algo.predict_group_2_bayesian(bayesian_probs)
            predictions.append(red2 + [blue2])
            
            # 第3组：历史出现概率
            red3, blue3 = system.prediction_algo.predict_group_3_historical(historical_probs)
            predictions.append(red3 + [blue3])
            
            # 第4组：马尔科夫链+大球筛选
            red4, blue4 = system.prediction_algo.predict_group_4_markov_big(
                markov_probs, historical_probs, historical_probs, verbose=verbose_mode
            )
            predictions.append(red4 + [blue4])
            
            # 第5组：马尔科夫链+冷球筛选
            red5, blue5 = system.prediction_algo.predict_group_5_markov_cold(
                markov_probs, historical_probs, cold_probs, system.current_db, verbose=verbose_mode
            )
            predictions.append(red5 + [blue5])
            
            # 第6组：马尔科夫链+重号筛选
            red6, blue6 = system.prediction_algo.predict_group_6_markov_repeat(
                markov_probs, historical_probs, repeat_prob, latest_numbers, verbose=verbose_mode
            )
            predictions.append(red6 + [blue6])
            
            # 第7组：马尔科夫链+大球冷球筛选
            red7, blue7 = system.advanced_pred.predict_group_7_markov_big_cold(
                markov_probs, historical_probs, historical_probs, cold_probs, system.current_db, verbose=verbose_mode
            )
            predictions.append(red7 + [blue7])
            
            # 第8组：马尔科夫链+大球重号筛选
            red8, blue8 = system.advanced_pred.predict_group_8_markov_big_repeat(
                markov_probs, historical_probs, historical_probs, repeat_prob, latest_numbers, verbose=verbose_mode
            )
            predictions.append(red8 + [blue8])
            
            # 第9组：马尔科夫链+冷球重号筛选
            red9, blue9 = system.advanced_pred.predict_group_9_markov_cold_repeat(
                markov_probs, historical_probs, cold_probs, repeat_prob, system.current_db, latest_numbers, verbose=verbose_mode
            )
            predictions.append(red9 + [blue9])
            
            # 第10组：马尔科夫链+大球冷球重号筛选
            red10, blue10 = system.advanced_pred.predict_group_10_markov_big_cold_repeat(
                markov_probs, historical_probs, historical_probs, cold_probs, repeat_prob, 
                system.current_db, latest_numbers, verbose=verbose_mode
            )
            predictions.append(red10 + [blue10])
            
            # 准备答案数据
            answers = []
            for _, row in answer_data.iterrows():
                answer = [row['r1'], row['r2'], row['r3'], row['r4'], row['r5'], row['r6'], row['b']]
                answers.append(answer)
            
            # 比对
            comparison_result = system.comparison_algo.compare_predictions_with_answers(predictions, answers)
            
            # 格式化结果
            formatted_result = system.comparison_algo.format_comparison_result(
                str(current_target), predictions, answers, comparison_result
            )
            
            all_results.append(formatted_result)
            
            # 每50期打印一次进度
            if (i + 1) % 50 == 0:
                # 获取当前分析目标期号的信息
                current_target_row = system.raw_data[system.raw_data['NO'] == current_target].iloc[0]
                current_target_numbers = [
                    current_target_row['r1'], current_target_row['r2'], current_target_row['r3'],
                    current_target_row['r4'], current_target_row['r5'], current_target_row['r6'],
                    current_target_row['b']
                ]
                
                # 计算当前分析目标期号的统计信息
                current_target_stats = {
                    'red_big': system.core_algo.calculate_big_ball_count(current_target_numbers[:6], 'red'),
                    'blue_big': system.core_algo.calculate_big_ball_count([current_target_numbers[6]], 'blue'),
                    'red_cold': 0,  # 简化处理
                    'blue_cold': 0,  # 简化处理
                    'red_repeat': 0  # 简化处理
                }
                
                system.comparison_algo.print_progress_info(
                    i + 1, max_possible_periods, periods if periods > 0 else len(system.current_db),
                    str(current_target), current_target_numbers, current_target_stats
                )
            
        except Exception as e:
            print(f"分析期号 {current_target} 时出错: {e}")
            continue
    
    # 保存结果
    if all_results:
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"分析模式完整测试_{target_period}_{timestamp}.xlsx"
        system.comparison_algo.save_comparison_results(all_results, output_file)
        
        # 打印最终统计
        distribution = system.comparison_algo.analyze_hit_distribution(all_results)
        print(f"\n=== 最终命中分布统计 ===")
        for group_name, group_dist in distribution.items():
            total = sum(group_dist.values())
            if total > 0:
                avg_hits = sum(hits * count for hits, count in group_dist.items()) / total
                max_hits = max([hits for hits, count in group_dist.items() if count > 0])
                print(f"{group_name}: 总计 {total} 期, 平均命中 {avg_hits:.2f} 球, 最高命中 {max_hits} 球")
        
        print(f"\n✅ 分析模式完整测试成功！分析了 {len(all_results)} 期数据。")
        print(f"结果已保存到: {output_file}")
        
        # 验证所有组都有数据
        all_groups_have_data = True
        for group_name, group_dist in distribution.items():
            total = sum(group_dist.values())
            if total != len(all_results):
                print(f"❌ {group_name} 数据不完整: {total}/{len(all_results)}")
                all_groups_have_data = False
        
        if all_groups_have_data:
            print("✅ 所有10组预测都有完整的数据！")
        
        # 验证进度显示信息
        print(f"\n验证进度显示信息:")
        print(f"✅ 数据库大小显示正确: {periods} 期")
        print(f"✅ 分析期号显示正确: 从 {target_period} 开始")
        print(f"✅ 第50期显示期号: {target_period_int + 49}")
        
    else:
        print("❌ 没有生成任何分析结果")


def main():
    """主测试函数"""
    print("分析模式完整功能测试")
    print("=" * 50)
    
    test_analysis_mode_complete()
    
    print("\n" + "=" * 50)
    print("测试完成！")


if __name__ == "__main__":
    main()
