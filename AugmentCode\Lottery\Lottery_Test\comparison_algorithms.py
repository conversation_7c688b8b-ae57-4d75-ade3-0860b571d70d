"""
比对算法模块
用于预测结果与实际开奖结果的比对分析
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Any


class ComparisonAlgorithms:
    """比对算法类"""
    
    def __init__(self):
        """初始化比对算法"""
        pass
    
    def compare_prediction_with_answer(self, predicted_numbers: List[int], 
                                     answer_numbers: List[int]) -> Tuple[int, bool]:
        """
        比对预测号码与答案号码
        
        Args:
            predicted_numbers: 预测号码 [红球1-6, 蓝球]
            answer_numbers: 答案号码 [红球1-6, 蓝球]
            
        Returns:
            (命中总数, 蓝球是否命中)
        """
        # 分离红球和蓝球
        pred_red = set(predicted_numbers[:6])
        pred_blue = predicted_numbers[6]
        
        ans_red = set(answer_numbers[:6])
        ans_blue = answer_numbers[6]
        
        # 计算红球命中数
        red_hits = len(pred_red.intersection(ans_red))
        
        # 计算蓝球命中
        blue_hit = 1 if pred_blue == ans_blue else 0
        
        # 总命中数
        total_hits = red_hits + blue_hit
        
        return total_hits, blue_hit == 1
    
    def compare_predictions_with_answers(self, predictions: List[List[int]], 
                                       answers: List[List[int]]) -> Dict[str, Any]:
        """
        比对多组预测号码与多期答案号码
        
        Args:
            predictions: 预测号码列表，每个元素是[红球1-6, 蓝球]
            answers: 答案号码列表，每个元素是[红球1-6, 蓝球]
            
        Returns:
            比对结果字典
        """
        results = {}
        
        for i, prediction in enumerate(predictions):
            group_name = f"第{i+1}组"
            max_hits = 0
            best_match_blue = False
            best_match_answer = None
            
            # 与每期答案比对
            for j, answer in enumerate(answers):
                hits, blue_hit = self.compare_prediction_with_answer(prediction, answer)
                
                if hits > max_hits:
                    max_hits = hits
                    best_match_blue = blue_hit
                    best_match_answer = j + 1  # 期号从1开始
            
            results[group_name] = {
                'prediction': prediction,
                'max_hits': max_hits,
                'blue_hit_in_best': best_match_blue,
                'best_match_period': best_match_answer
            }
        
        return results
    
    def analyze_hit_distribution(self, all_results: List[Dict[str, Any]]) -> Dict[str, Dict[int, int]]:
        """
        分析命中情况分布统计
        
        Args:
            all_results: 所有期数的比对结果列表
            
        Returns:
            每组预测的命中分布统计
        """
        distribution = {}
        
        # 初始化统计字典
        for i in range(10):  # 10组预测
            group_name = f"第{i+1}组"
            distribution[group_name] = {j: 0 for j in range(8)}  # 0-7个球的命中次数
        
        # 统计每组的命中分布
        for result in all_results:
            # 检查result的结构
            if 'predictions' in result:
                predictions_data = result['predictions']
                for group_name, group_result in predictions_data.items():
                    if isinstance(group_result, dict) and 'max_hits' in group_result:
                        max_hits = group_result['max_hits']
                        if group_name in distribution:
                            distribution[group_name][max_hits] += 1
        
        return distribution
    
    def format_comparison_result(self, period_no: str, predictions: List[List[int]], 
                               answers: List[List[int]], comparison_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化比对结果用于保存
        
        Args:
            period_no: 期号
            predictions: 预测号码列表
            answers: 答案号码列表
            comparison_result: 比对结果
            
        Returns:
            格式化的结果字典
        """
        formatted_result = {
            'period': period_no,
            'predictions': {},
            'answers': []
        }
        
        # 格式化答案
        for i, answer in enumerate(answers):
            red_balls = answer[:6]
            blue_ball = answer[6]
            formatted_result['answers'].append({
                'period': i + 1,
                'red_balls': red_balls,
                'blue_ball': blue_ball,
                'formatted': f"{' '.join(map(str, sorted(red_balls)))} + {blue_ball}"
            })
        
        # 格式化预测结果
        for group_name, group_result in comparison_result.items():
            prediction = group_result['prediction']
            red_balls = prediction[:6]
            blue_ball = prediction[6]
            
            formatted_result['predictions'][group_name] = {
                'red_balls': red_balls,
                'blue_ball': blue_ball,
                'formatted': f"{' '.join(map(str, sorted(red_balls)))} + {blue_ball}",
                'max_hits': group_result['max_hits'],
                'blue_hit_in_best': group_result['blue_hit_in_best'],
                'best_match_period': group_result['best_match_period']
            }
        
        return formatted_result
    
    def save_comparison_results(self, all_results: List[Dict[str, Any]], 
                              output_file: str) -> None:
        """
        保存比对结果到Excel文件
        
        Args:
            all_results: 所有比对结果
            output_file: 输出文件路径
        """
        try:
            # 创建Excel写入器
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                
                # 1. 详细比对结果
                detailed_data = []
                for result in all_results:
                    period = result['period']
                    
                    # 答案数据
                    answers_str = []
                    for answer in result['answers']:
                        answers_str.append(answer['formatted'])
                    
                    # 预测数据
                    row_data = {'期号': period, '答案号码': '; '.join(answers_str)}
                    
                    for group_name, group_result in result['predictions'].items():
                        row_data[f'{group_name}_预测'] = group_result['formatted']
                        row_data[f'{group_name}_命中'] = group_result['max_hits']
                        row_data[f'{group_name}_蓝球'] = '是' if group_result['blue_hit_in_best'] else '否'
                    
                    detailed_data.append(row_data)
                
                detailed_df = pd.DataFrame(detailed_data)
                detailed_df.to_excel(writer, sheet_name='详细比对结果', index=False)
                
                # 2. 命中分布统计
                distribution = self.analyze_hit_distribution(all_results)
                
                dist_data = []
                for group_name, group_dist in distribution.items():
                    row = {'预测组': group_name}
                    for hits in range(8):
                        row[f'{hits}球命中'] = group_dist[hits]
                    dist_data.append(row)
                
                dist_df = pd.DataFrame(dist_data)
                dist_df.to_excel(writer, sheet_name='命中分布统计', index=False)
                
                # 3. 汇总统计
                summary_data = []
                for group_name, group_dist in distribution.items():
                    total_periods = sum(group_dist.values())
                    avg_hits = sum(hits * count for hits, count in group_dist.items()) / total_periods if total_periods > 0 else 0
                    
                    max_hits_count = max(group_dist.values())
                    best_performance = [hits for hits, count in group_dist.items() if count == max_hits_count][0]
                    
                    summary_data.append({
                        '预测组': group_name,
                        '总期数': total_periods,
                        '平均命中': round(avg_hits, 2),
                        '最佳表现': f'{best_performance}球命中{max_hits_count}次',
                        '5球以上': group_dist[5] + group_dist[6] + group_dist[7],
                        '4球以上': group_dist[4] + group_dist[5] + group_dist[6] + group_dist[7]
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
            
            print(f"比对结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"保存比对结果失败: {e}")
    
    def print_progress_info(self, completed_periods: int, total_periods: int, 
                          current_db_size: int, latest_period: str, 
                          latest_numbers: List[int], latest_stats: Dict[str, int]) -> None:
        """
        打印分析进度信息
        
        Args:
            completed_periods: 已完成期数
            total_periods: 总期数
            current_db_size: 当前数据库期数
            latest_period: 最新期号
            latest_numbers: 最新期号码
            latest_stats: 最新期统计信息
        """
        print(f"\n=== 分析进度 ===")
        print(f"已完成: {completed_periods}/{total_periods} 期")
        print(f"当前数据库包含: {current_db_size} 期数据")
        print(f"当前最新1期号码: {latest_period} {' '.join(map(str, latest_numbers[:6]))} + {latest_numbers[6]}")
        print(f"最新1期红球大球数: {latest_stats['red_big']}")
        print(f"最新1期蓝球大球数: {latest_stats['blue_big']}")
        print(f"最新1期红球冷球数: {latest_stats['red_cold']}")
        print(f"最新1期蓝球冷球数: {latest_stats['blue_cold']}")
        print(f"最新1期红球重号数: {latest_stats['red_repeat']}")
        print("=" * 50)
