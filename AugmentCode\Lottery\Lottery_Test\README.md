# 双色球彩票预测选号与分析比对程序

## 项目简介

这是一个基于Python开发的双色球彩票预测分析系统，实现了多种预测算法和分析比对功能。系统采用模块化设计，包含数据加载、核心算法、预测算法、比对算法和用户交互等模块。

## 功能特性

### 1. 预测选号功能
- **10组不同的预测算法**：
  1. 马尔科夫链预测
  2. 贝叶斯概率预测
  3. 历史出现概率预测
  4. 马尔科夫链+大球筛选
  5. 马尔科夫链+冷球筛选
  6. 马尔科夫链+重号筛选
  7. 马尔科夫链+大球冷球筛选
  8. 马尔科夫链+大球重号筛选
  9. 马尔科夫链+冷球重号筛选
  10. 马尔科夫链+大球冷球重号筛选

- **统计分析**：
  - 历史出现概率统计
  - 跟随性概率矩阵
  - 大球数、冷球数、重号数分析
  - 马尔科夫链和贝叶斯概率计算

### 2. 分析比对功能
- 预测结果与实际开奖结果的比对
- 命中情况统计分析
- 批量历史数据分析
- 预测准确性评估

### 3. 数据管理
- Excel数据文件读取
- 数据有效性验证
- 灵活的数据库范围设置
- 结果保存和导出

## 文件结构

```
Lottery_Test/
├── main.py                    # 主程序入口
├── data_loader.py            # 数据加载模块
├── core_algorithms.py        # 核心算法模块
├── prediction_algorithms.py  # 基础预测算法模块
├── advanced_predictions.py   # 高级预测算法模块
├── comparison_algorithms.py  # 比对算法模块
├── user_interface.py         # 用户交互模块
├── lottery_data_all.xlsx     # 历史数据文件
├── test_prediction.py        # 预测功能测试脚本
├── test_full_prediction.py   # 完整预测测试脚本
├── test_analysis.py          # 分析功能测试脚本
└── README.md                 # 说明文档
```

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖库
```bash
pip install pandas numpy openpyxl
```

## 使用方法

### 1. 运行主程序
```bash
python main.py
```

### 2. 选择功能
程序启动后会提示选择功能：
- 输入 `1`：预测选号
- 输入 `2`：分析比对

### 3. 预测选号模式
1. 选择数据库范围：
   - 输入 `0`：使用全部历史数据
   - 输入正整数（如 `500`）：使用最新N期数据

2. 程序会自动计算各种概率并生成10组预测号码

3. 选择是否保存统计表格到Excel文件

### 4. 分析比对模式
1. 输入目标期号（分析的起始期号）
2. 选择数据库范围
3. 程序会自动进行批量分析并保存结果

### 5. 测试脚本
- `test_prediction.py`：测试基础预测功能
- `test_full_prediction.py`：测试完整预测功能
- `test_analysis.py`：测试分析比对功能

## 核心算法说明

### 1. 术语定义
- **期号**：每期的编号，前两位表示年份，后三位表示序号
- **大球数**：红球中号码>16的个数，蓝球中号码>8的个数
- **冷球数**：当前期中未在前5期出现的号码个数
- **重号数**：与上一期相同的红球号码个数

### 2. 核心算法
- **历史出现概率**：统计各号码的历史出现频率
- **跟随性概率**：统计相邻两期号码的跟随关系
- **马尔科夫链**：基于状态转移的预测算法
- **贝叶斯概率**：结合先验概率和条件概率的预测方法

### 3. 预测策略
- 基础预测：单一算法预测
- 复合预测：多重条件筛选的预测算法
- 约束优化：满足特定统计特征的预测调整

## 输出文件

### 1. 预测结果文件
- `SSQ_统计表格_YYYYMMDD_HHMMSS.xlsx`
- 包含所有概率表格、预测结果等

### 2. 分析比对文件
- `分析比对结果_期号_YYYYMMDD_HHMMSS.xlsx`
- 包含详细比对结果、命中分布统计等

## 数据格式要求

### Excel数据文件格式
- 文件名：`lottery_data_all.xlsx`
- 工作表名：`SSQ_data_all`
- 列结构：
  - A列：期号 (NO)
  - I-N列：红球号码 (r1-r6)
  - O列：蓝球号码 (b)

## 注意事项

1. **数据完整性**：确保历史数据文件完整且格式正确
2. **内存使用**：大量数据分析时可能占用较多内存
3. **计算时间**：复杂预测算法可能需要较长计算时间
4. **结果解释**：预测结果仅供参考，不保证准确性

## 技术特点

1. **模块化设计**：各功能模块独立，便于维护和扩展
2. **算法多样性**：集成多种预测算法，提供不同角度的分析
3. **数据验证**：完善的数据验证机制，确保计算准确性
4. **结果可视化**：详细的统计表格和分析报告
5. **用户友好**：简洁的交互界面，易于使用

## 开发信息

- **开发语言**：Python 3.x
- **主要依赖**：pandas, numpy, openpyxl
- **设计模式**：面向对象编程
- **代码风格**：PEP 8标准

## 版本历史

- **v1.0**：基础功能实现
  - 数据加载和验证
  - 10组预测算法
  - 分析比对功能
  - 用户交互界面

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目地址：AugmentCode/Lottery/Lottery_Test
- 开发时间：2025年7月

---

**免责声明**：本程序仅用于技术研究和学习目的，预测结果不构成任何投注建议。彩票具有随机性，请理性对待。
