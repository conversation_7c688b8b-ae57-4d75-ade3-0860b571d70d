"""
测试分析比对功能的脚本
"""

from main import LotteryPredictionSystem


def test_analysis():
    """测试分析比对功能"""
    try:
        # 创建系统实例
        system = LotteryPredictionSystem()
        
        # 初始化数据
        print("正在初始化数据...")
        if not system.initialize_data():
            print("数据初始化失败")
            return
        
        # 获取可用期号列表
        available_periods = [str(int(period)) for period in system.raw_data['NO'].tolist()]
        print(f"可用期号范围: {available_periods[0]} - {available_periods[-1]}")
        
        # 选择一个测试期号（选择一个较早的期号以确保有足够的后续数据）
        target_period = "25050"  # 选择一个测试期号
        target_period_int = int(target_period)
        
        # 检查期号是否存在
        if target_period not in available_periods:
            print(f"期号 {target_period} 不存在，选择第一个可用期号")
            target_period = available_periods[100]  # 选择第100个期号作为测试
            target_period_int = int(target_period)
        
        print(f"测试期号: {target_period}")
        
        # 设置数据库范围
        periods = 500  # 使用500期数据
        
        # 计算可分析期数
        target_idx = system.raw_data[system.raw_data['NO'] == target_period_int].index[0]
        max_possible_periods = len(system.raw_data) - target_idx - 6  # 需要6期答案数据
        
        if max_possible_periods <= 0:
            print("数据不足，无法进行分析比对")
            return
        
        # 限制测试期数
        test_periods = min(10, max_possible_periods)  # 只测试10期
        
        print(f"将测试 {test_periods} 期的分析比对")
        
        # 开始分析比对
        all_results = []
        
        for i in range(test_periods):
            current_target = target_period_int + i
            print(f"正在分析期号: {current_target} ({i+1}/{test_periods})")
            
            # 更新当前数据库
            current_target_idx = system.raw_data[system.raw_data['NO'] == current_target].index[0]
            start_idx = max(0, current_target_idx + 1 - periods)
            system.current_db = system.raw_data.iloc[start_idx:current_target_idx + 1].copy()
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(current_target, 6)
            if answer_data is None:
                print(f"期号 {current_target} 后续数据不足，跳过")
                break
            
            # 进行预测
            try:
                # 获取最新期信息
                latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
                
                # 计算必要的概率
                historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
                follow_matrices = system.core_algo.calculate_following_probability(system.current_db)
                markov_probs = system.core_algo.calculate_markov_chain(
                    latest_numbers, follow_matrices, historical_probs
                )
                bayesian_probs = system.core_algo.calculate_bayesian_probability(
                    latest_numbers, follow_matrices, historical_probs
                )
                
                # 预测（只做前3组以节省时间）
                predictions = []
                
                # 第1组：马尔科夫链
                red1, blue1 = system.prediction_algo.predict_group_1_markov(markov_probs)
                predictions.append(red1 + [blue1])
                
                # 第2组：贝叶斯概率
                red2, blue2 = system.prediction_algo.predict_group_2_bayesian(bayesian_probs)
                predictions.append(red2 + [blue2])
                
                # 第3组：历史出现概率
                red3, blue3 = system.prediction_algo.predict_group_3_historical(historical_probs)
                predictions.append(red3 + [blue3])
                
                # 准备答案数据
                answers = []
                for _, row in answer_data.iterrows():
                    answer = [row['r1'], row['r2'], row['r3'], row['r4'], row['r5'], row['r6'], row['b']]
                    answers.append(answer)
                
                # 比对
                comparison_result = system.comparison_algo.compare_predictions_with_answers(predictions, answers)
                
                # 格式化结果
                formatted_result = system.comparison_algo.format_comparison_result(
                    str(current_target), predictions, answers, comparison_result
                )
                
                all_results.append(formatted_result)
                
                # 打印当前期的比对结果
                print(f"期号 {current_target} 比对结果:")
                for group_name, group_result in comparison_result.items():
                    pred = group_result['prediction']
                    red_str = ' '.join(map(str, sorted(pred[:6])))
                    print(f"  {group_name}: {red_str} + {pred[6]} -> 最大命中 {group_result['max_hits']} 球")
                
            except Exception as e:
                print(f"分析期号 {current_target} 时出错: {e}")
                continue
        
        # 保存结果
        if all_results:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"测试分析比对结果_{target_period}_{timestamp}.xlsx"
            system.comparison_algo.save_comparison_results(all_results, output_file)
            
            # 打印最终统计
            distribution = system.comparison_algo.analyze_hit_distribution(all_results)
            print("\n=== 最终命中分布统计 ===")
            for group_name, group_dist in distribution.items():
                total = sum(group_dist.values())
                if total > 0:
                    avg_hits = sum(hits * count for hits, count in group_dist.items()) / total
                    max_hits = max([hits for hits, count in group_dist.items() if count > 0])
                    print(f"{group_name}: 平均命中 {avg_hits:.2f} 球, 最高命中 {max_hits} 球")
        
        print(f"\n测试完成！分析了 {len(all_results)} 期数据。")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_analysis()
