"""
测试预测功能的脚本
"""

from main import LotteryPredictionSystem


def test_prediction():
    """测试预测功能"""
    try:
        # 创建系统实例
        system = LotteryPredictionSystem()
        
        # 初始化数据
        print("正在初始化数据...")
        if not system.initialize_data():
            print("数据初始化失败")
            return
        
        # 设置测试参数
        periods = 500  # 使用最新500期数据
        system.current_db = system.data_loader.get_current_database(periods)
        
        # 获取最新期信息
        latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
        
        print(f"当前数据库包含: {len(system.current_db)} 期数据")
        red_str = ' '.join(map(str, sorted(latest_numbers[:6])))
        blue_str = str(latest_numbers[6])
        print(f"最新一期的号码为：{latest_period} {red_str} + {blue_str}")
        
        # 计算各种概率
        print("\n正在计算概率...")
        historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
        cold_probs = system.core_algo.calculate_cold_ball_probability(system.current_db)
        repeat_prob = system.core_algo.calculate_repeat_probability(system.current_db)
        follow_matrices = system.core_algo.calculate_following_probability(system.current_db)
        markov_probs = system.core_algo.calculate_markov_chain(
            latest_numbers, follow_matrices, historical_probs
        )
        bayesian_probs = system.core_algo.calculate_bayesian_probability(
            latest_numbers, follow_matrices, historical_probs
        )
        
        # 进行预测
        print("\n=== 预测结果 ===")
        
        # 第1组：马尔科夫链
        red1, blue1 = system.prediction_algo.predict_group_1_markov(markov_probs)
        system.ui.print_prediction_result(1, red1, blue1)
        
        # 第2组：贝叶斯概率
        red2, blue2 = system.prediction_algo.predict_group_2_bayesian(bayesian_probs)
        system.ui.print_prediction_result(2, red2, blue2)
        
        # 第3组：历史出现概率
        red3, blue3 = system.prediction_algo.predict_group_3_historical(historical_probs)
        system.ui.print_prediction_result(3, red3, blue3)
        
        print("\n测试完成！前3组预测算法运行正常。")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_prediction()
