"""
演示程序的完整功能
"""

from main import LotteryPredictionSystem


def demo_prediction():
    """演示预测功能"""
    print("=== 演示预测功能 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 模拟用户选择：使用最新500期数据
    periods = 500
    system.current_db = system.data_loader.get_current_database(periods)
    
    # 获取最新期信息
    latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
    
    # 打印头部信息
    system.ui.print_prediction_header(len(system.current_db), latest_period, latest_numbers)
    
    # 计算各种概率
    print("\n正在计算各种概率...")
    historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
    cold_probs = system.core_algo.calculate_cold_ball_probability(system.current_db)
    repeat_prob = system.core_algo.calculate_repeat_probability(system.current_db)
    follow_matrices = system.core_algo.calculate_following_probability(system.current_db)
    markov_probs = system.core_algo.calculate_markov_chain(
        latest_numbers, follow_matrices, historical_probs
    )
    bayesian_probs = system.core_algo.calculate_bayesian_probability(
        latest_numbers, follow_matrices, historical_probs
    )
    
    # 打印要求信息
    red_big_req = historical_probs['red_big_prob'].loc[
        historical_probs['red_big_prob']['probability'].idxmax(), 'big_count'
    ]
    blue_big_req = historical_probs['blue_big_prob'].loc[
        historical_probs['blue_big_prob']['probability'].idxmax(), 'big_count'
    ]
    red_cold_req = cold_probs['red_cold_prob'].loc[
        cold_probs['red_cold_prob']['probability'].idxmax(), 'cold_count'
    ]
    blue_cold_req = cold_probs['blue_cold_prob'].loc[
        cold_probs['blue_cold_prob']['probability'].idxmax(), 'cold_count'
    ]
    red_repeat_req = repeat_prob.loc[
        repeat_prob['probability'].idxmax(), 'repeat_count'
    ]
    
    system.ui.print_prediction_requirements(
        red_big_req, blue_big_req, red_cold_req, blue_cold_req, red_repeat_req
    )
    
    # 获取冷球信息
    red_cold_balls = system.prediction_algo.get_cold_balls(system.current_db, 'red')
    blue_cold_balls = system.prediction_algo.get_cold_balls(system.current_db, 'blue')
    system.ui.print_cold_balls_info(red_cold_balls, blue_cold_balls)
    
    # 进行所有10组预测
    print("\n=== 预测结果 ===")
    predictions = []
    
    # 第1组：马尔科夫链
    red1, blue1 = system.prediction_algo.predict_group_1_markov(markov_probs)
    system.ui.print_prediction_result(1, red1, blue1)
    predictions.append((red1, blue1))
    
    # 第2组：贝叶斯概率
    red2, blue2 = system.prediction_algo.predict_group_2_bayesian(bayesian_probs)
    system.ui.print_prediction_result(2, red2, blue2)
    predictions.append((red2, blue2))
    
    # 第3组：历史出现概率
    red3, blue3 = system.prediction_algo.predict_group_3_historical(historical_probs)
    system.ui.print_prediction_result(3, red3, blue3)
    predictions.append((red3, blue3))
    
    # 第4组：马尔科夫链+大球筛选
    red4, blue4 = system.prediction_algo.predict_group_4_markov_big(
        markov_probs, historical_probs, historical_probs
    )
    system.ui.print_prediction_result(4, red4, blue4)
    predictions.append((red4, blue4))
    
    # 第5组：马尔科夫链+冷球筛选
    red5, blue5 = system.prediction_algo.predict_group_5_markov_cold(
        markov_probs, historical_probs, cold_probs, system.current_db
    )
    system.ui.print_prediction_result(5, red5, blue5)
    predictions.append((red5, blue5))
    
    # 第6组：马尔科夫链+重号筛选
    red6, blue6 = system.prediction_algo.predict_group_6_markov_repeat(
        markov_probs, historical_probs, repeat_prob, latest_numbers
    )
    system.ui.print_prediction_result(6, red6, blue6)
    predictions.append((red6, blue6))
    
    # 第7组：马尔科夫链+大球冷球筛选
    red7, blue7 = system.advanced_pred.predict_group_7_markov_big_cold(
        markov_probs, historical_probs, historical_probs, cold_probs, system.current_db
    )
    system.ui.print_prediction_result(7, red7, blue7)
    predictions.append((red7, blue7))
    
    # 第8组：马尔科夫链+大球重号筛选
    red8, blue8 = system.advanced_pred.predict_group_8_markov_big_repeat(
        markov_probs, historical_probs, historical_probs, repeat_prob, latest_numbers
    )
    system.ui.print_prediction_result(8, red8, blue8)
    predictions.append((red8, blue8))
    
    # 第9组：马尔科夫链+冷球重号筛选
    red9, blue9 = system.advanced_pred.predict_group_9_markov_cold_repeat(
        markov_probs, historical_probs, cold_probs, repeat_prob, system.current_db, latest_numbers
    )
    system.ui.print_prediction_result(9, red9, blue9)
    predictions.append((red9, blue9))
    
    # 第10组：马尔科夫链+大球冷球重号筛选
    red10, blue10 = system.advanced_pred.predict_group_10_markov_big_cold_repeat(
        markov_probs, historical_probs, historical_probs, cold_probs, repeat_prob, 
        system.current_db, latest_numbers
    )
    system.ui.print_prediction_result(10, red10, blue10)
    predictions.append((red10, blue10))
    
    print(f"\n预测完成！共生成 {len(predictions)} 组预测号码。")
    
    # 保存统计表格
    all_tables = {
        'historical_probs': historical_probs,
        'cold_probs': cold_probs,
        'repeat_prob': repeat_prob,
        'follow_matrices': follow_matrices,
        'markov_probs': markov_probs,
        'bayesian_probs': bayesian_probs,
        'predictions': predictions
    }
    
    filename = system.ui.save_prediction_tables(all_tables, len(system.current_db))
    print(f"统计表格已保存到: {filename}")


def demo_analysis():
    """演示分析功能"""
    print("\n=== 演示分析功能 ===")
    
    # 创建系统实例
    system = LotteryPredictionSystem()
    
    # 初始化数据
    if not system.initialize_data():
        print("数据初始化失败")
        return
    
    # 选择测试期号
    target_period = "25060"
    target_period_int = int(target_period)
    periods = 500
    
    print(f"分析目标期号: {target_period}")
    print(f"使用数据库范围: 最新{periods}期")
    
    # 进行5期分析演示
    test_periods = 5
    all_results = []
    
    for i in range(test_periods):
        current_target = target_period_int + i
        print(f"\n正在分析期号: {current_target} ({i+1}/{test_periods})")
        
        # 更新当前数据库
        current_target_idx = system.raw_data[system.raw_data['NO'] == current_target].index[0]
        start_idx = max(0, current_target_idx + 1 - periods)
        system.current_db = system.raw_data.iloc[start_idx:current_target_idx + 1].copy()
        
        # 获取答案数据
        answer_data = system.data_loader.get_answer_data(current_target, 6)
        if answer_data is None:
            print(f"期号 {current_target} 后续数据不足，跳过")
            break
        
        # 进行预测
        try:
            # 获取最新期信息
            latest_period, latest_numbers, _ = system.data_loader.get_latest_period_info(system.current_db)
            
            # 计算必要的概率
            historical_probs = system.core_algo.calculate_historical_probability(system.current_db)
            follow_matrices = system.core_algo.calculate_following_probability(system.current_db)
            markov_probs = system.core_algo.calculate_markov_chain(
                latest_numbers, follow_matrices, historical_probs
            )
            bayesian_probs = system.core_algo.calculate_bayesian_probability(
                latest_numbers, follow_matrices, historical_probs
            )
            
            # 预测（前3组）
            predictions = []
            
            # 第1组：马尔科夫链
            red1, blue1 = system.prediction_algo.predict_group_1_markov(markov_probs)
            predictions.append(red1 + [blue1])
            
            # 第2组：贝叶斯概率
            red2, blue2 = system.prediction_algo.predict_group_2_bayesian(bayesian_probs)
            predictions.append(red2 + [blue2])
            
            # 第3组：历史出现概率
            red3, blue3 = system.prediction_algo.predict_group_3_historical(historical_probs)
            predictions.append(red3 + [blue3])
            
            # 准备答案数据
            answers = []
            for _, row in answer_data.iterrows():
                answer = [row['r1'], row['r2'], row['r3'], row['r4'], row['r5'], row['r6'], row['b']]
                answers.append(answer)
            
            # 比对
            comparison_result = system.comparison_algo.compare_predictions_with_answers(predictions, answers)
            
            # 格式化结果
            formatted_result = system.comparison_algo.format_comparison_result(
                str(current_target), predictions, answers, comparison_result
            )
            
            all_results.append(formatted_result)
            
            # 打印当前期的比对结果
            print(f"期号 {current_target} 比对结果:")
            for group_name, group_result in comparison_result.items():
                pred = group_result['prediction']
                red_str = ' '.join(map(str, sorted(pred[:6])))
                print(f"  {group_name}: {red_str} + {pred[6]} -> 最大命中 {group_result['max_hits']} 球")
            
        except Exception as e:
            print(f"分析期号 {current_target} 时出错: {e}")
            continue
    
    # 保存结果
    if all_results:
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"演示分析比对结果_{target_period}_{timestamp}.xlsx"
        system.comparison_algo.save_comparison_results(all_results, output_file)
        
        # 打印最终统计
        distribution = system.comparison_algo.analyze_hit_distribution(all_results)
        print("\n=== 最终命中分布统计 ===")
        for group_name, group_dist in distribution.items():
            total = sum(group_dist.values())
            if total > 0:
                avg_hits = sum(hits * count for hits, count in group_dist.items()) / total
                max_hits = max([hits for hits, count in group_dist.items() if count > 0])
                print(f"{group_name}: 平均命中 {avg_hits:.2f} 球, 最高命中 {max_hits} 球")
    
    print(f"\n分析演示完成！分析了 {len(all_results)} 期数据。")


def main():
    """主演示函数"""
    print("双色球彩票预测选号与分析比对程序演示")
    print("=" * 50)
    
    # 演示预测功能
    demo_prediction()
    
    # 演示分析功能
    demo_analysis()
    
    print("\n" + "=" * 50)
    print("演示完成！程序功能正常。")
    print("要使用完整功能，请运行: python main.py")


if __name__ == "__main__":
    main()
