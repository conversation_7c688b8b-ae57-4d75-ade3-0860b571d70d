"""
数据加载模块
负责从Excel文件中读取双色球历史数据
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional


class DataLoader:
    """数据加载器类"""
    
    def __init__(self, file_path: str = "lottery_data_all.xlsx"):
        """
        初始化数据加载器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.raw_data = None
        
    def load_data(self) -> pd.DataFrame:
        """
        从Excel文件中读取SSQ数据
        
        Returns:
            包含历史数据的DataFrame
        """
        try:
            # 读取SSQ_data_all标签页的A列至O列数据
            df = pd.read_excel(self.file_path, sheet_name='SSQ_data_all')
            
            # 选择需要的列：A列(期号)、I列至O列(红球和蓝球)
            # 根据实际数据结构，A列是NO，I-N列是r1-r6(红球)，O列是b(蓝球)
            selected_columns = ['NO', 'r1', 'r2', 'r3', 'r4', 'r5', 'r6', 'b']
            df_selected = df[selected_columns].copy()
            
            # 清除无效数据和空数据行
            df_selected = df_selected.dropna()
            
            # 按期号从小到大排序
            df_selected = df_selected.sort_values('NO')
            
            # 重置索引
            df_selected = df_selected.reset_index(drop=True)
            
            self.raw_data = df_selected
            print(f"成功加载数据，共{len(df_selected)}期")
            
            return df_selected
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            raise
    
    def get_current_database(self, periods: int = 0) -> pd.DataFrame:
        """
        根据用户输入获取当前数据库范围
        
        Args:
            periods: 期数，0表示使用全部数据，正整数表示最新N期数据
            
        Returns:
            当前数据库DataFrame
        """
        if self.raw_data is None:
            self.load_data()
            
        if periods == 0:
            # 使用全部数据
            current_db = self.raw_data.copy()
        else:
            # 使用最新N期数据
            current_db = self.raw_data.tail(periods).copy()
            
        return current_db
    
    def get_latest_period_info(self, current_db: pd.DataFrame) -> Tuple[str, list, int]:
        """
        获取最新一期的信息
        
        Args:
            current_db: 当前数据库
            
        Returns:
            (期号, [红球1-6, 蓝球], 期号整数值)
        """
        latest_row = current_db.iloc[-1]
        period_no = latest_row['NO']
        red_balls = [latest_row['r1'], latest_row['r2'], latest_row['r3'], 
                    latest_row['r4'], latest_row['r5'], latest_row['r6']]
        blue_ball = latest_row['b']
        
        numbers = red_balls + [blue_ball]
        
        return str(int(period_no)), numbers, int(period_no)
    
    def get_answer_data(self, target_period: int, periods: int = 6) -> Optional[pd.DataFrame]:
        """
        获取目标期号之后的连续N期数据作为答案数据
        
        Args:
            target_period: 目标期号
            periods: 需要的期数，默认6期
            
        Returns:
            答案数据DataFrame，如果数据不足则返回None
        """
        if self.raw_data is None:
            self.load_data()
            
        # 找到目标期号的位置
        target_idx = self.raw_data[self.raw_data['NO'] == target_period].index
        
        if len(target_idx) == 0:
            print(f"未找到期号 {target_period}")
            return None
            
        target_idx = target_idx[0]
        
        # 获取目标期号之后的连续periods期数据
        start_idx = target_idx + 1
        end_idx = start_idx + periods
        
        if end_idx > len(self.raw_data):
            print(f"数据不足，无法获取期号 {target_period} 之后的 {periods} 期数据")
            return None
            
        answer_data = self.raw_data.iloc[start_idx:end_idx].copy()
        
        return answer_data
    
    def validate_data(self, df: pd.DataFrame) -> bool:
        """
        验证数据的有效性
        
        Args:
            df: 要验证的DataFrame
            
        Returns:
            数据是否有效
        """
        try:
            # 检查红球范围 1-33
            red_columns = ['r1', 'r2', 'r3', 'r4', 'r5', 'r6']
            for col in red_columns:
                if not df[col].between(1, 33).all():
                    print(f"红球数据 {col} 超出范围 1-33")
                    return False
            
            # 检查蓝球范围 1-16
            if not df['b'].between(1, 16).all():
                print("蓝球数据超出范围 1-16")
                return False
                
            # 检查期号格式
            if not df['NO'].dtype in ['int64', 'float64']:
                print("期号格式不正确")
                return False
                
            return True
            
        except Exception as e:
            print(f"数据验证失败: {e}")
            return False
