"""
用户交互模块
处理用户输入和程序交互
"""

import pandas as pd
import numpy as np
from typing import Tuple, Optional, List
from datetime import datetime


class UserInterface:
    """用户交互类"""
    
    def __init__(self):
        """初始化用户交互"""
        pass
    
    def get_main_function_choice(self) -> int:
        """
        获取用户选择的主要功能
        
        Returns:
            功能选择 (1: 预测选号, 2: 分析比对)
        """
        while True:
            try:
                print("\n=== 双色球彩票预测分析系统 ===")
                print("请选择功能：")
                print("1) 预测选号")
                print("2) 分析比对")
                
                choice = int(input("请输入选择 (1 或 2): ").strip())
                
                if choice in [1, 2]:
                    return choice
                else:
                    print("输入错误，请输入 1 或 2")
                    
            except ValueError:
                print("输入错误，请输入数字 1 或 2")
    
    def get_database_range(self, mode: str = "prediction") -> int:
        """
        获取用户指定的数据库范围
        
        Args:
            mode: 模式 ("prediction" 或 "analysis")
            
        Returns:
            期数 (0表示全部数据，正整数表示最新N期)
        """
        while True:
            try:
                if mode == "prediction":
                    print("\n请指定当前数据库的数据范围：")
                    print("输入 0：使用全部历史数据")
                    print("输入正整数（如500）：使用最新N期数据")
                else:
                    print("\n请指定当前数据库的数据范围：")
                    print("输入 0：使用目标期号及之前的所有数据")
                    print("输入正整数（如500）：使用目标期号及之前的N期数据")
                
                periods = int(input("请输入期数: ").strip())
                
                if periods >= 0:
                    return periods
                else:
                    print("输入错误，请输入非负整数")
                    
            except ValueError:
                print("输入错误，请输入数字")
    
    def get_target_period(self, available_periods: List[str]) -> str:
        """
        获取用户指定的目标期号
        
        Args:
            available_periods: 可用的期号列表
            
        Returns:
            目标期号字符串
        """
        while True:
            try:
                print(f"\n可用期号范围: {available_periods[0]} - {available_periods[-1]}")
                print("请输入想要开始分析比对的期号：")
                
                target = input("期号: ").strip()
                
                # 验证期号是否存在
                if target in available_periods:
                    return target
                else:
                    print(f"期号 {target} 不存在，请重新输入")
                    
            except Exception as e:
                print(f"输入错误: {e}")
    
    def ask_save_tables(self) -> bool:
        """
        询问用户是否保存统计表格
        
        Returns:
            是否保存
        """
        while True:
            try:
                choice = input("\n是否保存统计表格？(y/n): ").strip().lower()
                
                if choice in ['y', 'yes', '是']:
                    return True
                elif choice in ['n', 'no', '否']:
                    return False
                else:
                    print("请输入 y/n 或 是/否")
                    
            except Exception as e:
                print(f"输入错误: {e}")
    
    def print_prediction_header(self, current_db_size: int, latest_period: str, 
                              latest_numbers: List[int]) -> None:
        """
        打印预测功能的头部信息
        
        Args:
            current_db_size: 当前数据库大小
            latest_period: 最新期号
            latest_numbers: 最新期号码
        """
        print(f"\n=== 预测选号 ===")
        print(f"当前数据库包含: {current_db_size} 期数据")
        red_str = ' '.join(map(str, sorted(latest_numbers[:6])))
        blue_str = str(latest_numbers[6])
        print(f"最新一期的号码为：{latest_period} {red_str} + {blue_str}")
    
    def print_analysis_header(self, total_periods: int, start_period: str, 
                            current_db_size: int) -> None:
        """
        打印分析功能的头部信息
        
        Args:
            total_periods: 总分析期数
            start_period: 开始期号
            current_db_size: 当前数据库大小
        """
        print(f"\n=== 分析比对 ===")
        print(f"需要分析比对的总期数: {total_periods}")
        print(f"开始期号: {start_period}")
        print(f"当前数据库包含: {current_db_size} 期数据")
    
    def print_prediction_requirements(self, red_big: int, blue_big: int, 
                                    red_cold: int, blue_cold: int, 
                                    red_repeat: int) -> None:
        """
        打印预测要求信息
        
        Args:
            red_big: 要求的红球大球数
            blue_big: 要求的蓝球大球数
            red_cold: 要求的红球冷球数
            blue_cold: 要求的蓝球冷球数
            red_repeat: 要求的红球重号数
        """
        print(f"基于历史出现概率要求的红蓝球大球数：{red_big}、{blue_big}")
        print(f"基于历史出现概率要求的红蓝球冷球数：{red_cold}、{blue_cold}")
        print(f"基于历史出现概率要求的红球重号数：{red_repeat}")
    
    def print_cold_balls_info(self, red_cold_balls: List[int], 
                            blue_cold_balls: List[int]) -> None:
        """
        打印冷球信息
        
        Args:
            red_cold_balls: 红球冷球号码列表
            blue_cold_balls: 蓝球冷球号码列表
        """
        print(f"红球冷球号码：{red_cold_balls}")
        print(f"蓝球冷球号码：{blue_cold_balls}")
    
    def print_prediction_result(self, group_num: int, red_balls: List[int], 
                              blue_ball: int) -> None:
        """
        打印预测结果
        
        Args:
            group_num: 组号
            red_balls: 红球号码列表
            blue_ball: 蓝球号码
        """
        red_sorted = sorted(red_balls)
        red_str = ' '.join(map(str, red_sorted))
        print(f"第{group_num}组预测号码为：{red_str} + {blue_ball}")
    
    def save_prediction_tables(self, all_tables: dict, current_db_size: int) -> str:
        """
        保存预测相关的统计表格
        
        Args:
            all_tables: 所有统计表格字典
            current_db_size: 当前数据库大小
            
        Returns:
            保存的文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"SSQ_统计表格_{timestamp}.xlsx"
            
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                
                # 保存历史出现概率表
                if 'historical_probs' in all_tables:
                    hist_probs = all_tables['historical_probs']
                    hist_probs['red_number_prob'].to_excel(writer, sheet_name='红球号码历史概率', index=False)
                    hist_probs['blue_number_prob'].to_excel(writer, sheet_name='蓝球号码历史概率', index=False)
                    hist_probs['red_big_prob'].to_excel(writer, sheet_name='红球大球数概率', index=False)
                    hist_probs['blue_big_prob'].to_excel(writer, sheet_name='蓝球大球数概率', index=False)
                
                # 保存冷球概率表
                if 'cold_probs' in all_tables:
                    cold_probs = all_tables['cold_probs']
                    cold_probs['red_cold_prob'].to_excel(writer, sheet_name='红球冷球数概率', index=False)
                    cold_probs['blue_cold_prob'].to_excel(writer, sheet_name='蓝球冷球数概率', index=False)
                
                # 保存重号概率表
                if 'repeat_prob' in all_tables:
                    all_tables['repeat_prob'].to_excel(writer, sheet_name='红球重号数概率', index=False)
                
                # 保存跟随性概率矩阵
                if 'follow_matrices' in all_tables:
                    follow_matrices = all_tables['follow_matrices']
                    
                    # 红球跟随性概率矩阵
                    red_follow_df = pd.DataFrame(follow_matrices['red_follow_matrix'])
                    red_follow_df.index = [f'红球{i+1}' for i in range(33)]
                    red_follow_df.columns = [f'红球{i+1}' for i in range(33)]
                    red_follow_df.to_excel(writer, sheet_name='红球跟随性概率矩阵')
                    
                    # 蓝球跟随性概率矩阵
                    blue_follow_df = pd.DataFrame(follow_matrices['blue_follow_matrix'])
                    blue_follow_df.index = [f'蓝球{i+1}' for i in range(16)]
                    blue_follow_df.columns = [f'蓝球{i+1}' for i in range(16)]
                    blue_follow_df.to_excel(writer, sheet_name='蓝球跟随性概率矩阵')
                
                # 保存马尔科夫链概率
                if 'markov_probs' in all_tables:
                    markov_probs = all_tables['markov_probs']
                    
                    red_markov_df = pd.DataFrame({
                        'red_number': range(1, 34),
                        'markov_probability': markov_probs['red_markov']
                    })
                    red_markov_df.to_excel(writer, sheet_name='红球马尔科夫概率', index=False)
                    
                    blue_markov_df = pd.DataFrame({
                        'blue_number': range(1, 17),
                        'markov_probability': markov_probs['blue_markov']
                    })
                    blue_markov_df.to_excel(writer, sheet_name='蓝球马尔科夫概率', index=False)
                
                # 保存贝叶斯概率
                if 'bayesian_probs' in all_tables:
                    bayesian_probs = all_tables['bayesian_probs']
                    
                    red_bayesian_df = pd.DataFrame({
                        'red_number': range(1, 34),
                        'bayesian_probability': bayesian_probs['red_bayesian']
                    })
                    red_bayesian_df.to_excel(writer, sheet_name='红球贝叶斯概率', index=False)
                    
                    blue_bayesian_df = pd.DataFrame({
                        'blue_number': range(1, 17),
                        'bayesian_probability': bayesian_probs['blue_bayesian']
                    })
                    blue_bayesian_df.to_excel(writer, sheet_name='蓝球贝叶斯概率', index=False)
                
                # 保存预测结果
                if 'predictions' in all_tables:
                    predictions = all_tables['predictions']
                    pred_data = []
                    
                    for i, (red_balls, blue_ball) in enumerate(predictions):
                        red_str = ' '.join(map(str, sorted(red_balls)))
                        pred_data.append({
                            '预测组': f'第{i+1}组',
                            '红球号码': red_str,
                            '蓝球号码': blue_ball,
                            '完整号码': f"{red_str} + {blue_ball}"
                        })
                    
                    pred_df = pd.DataFrame(pred_data)
                    pred_df.to_excel(writer, sheet_name='预测结果汇总', index=False)
            
            print(f"统计表格已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"保存统计表格失败: {e}")
            return ""
